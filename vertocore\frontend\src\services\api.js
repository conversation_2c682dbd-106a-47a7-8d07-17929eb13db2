/**
 * API Service for VertoAI
 * Handles communication with the backend API
 */
import { USE_SEPARATE_TITLE_KEY, API_URL } from '../utils/config';

/**
 * Simple cache for API responses to prevent duplicate requests
 */
const apiCache = {
  cache: new Map(),

  // Generate a cache key from request parameters
  getKey: (prompt, provider, domain, apiKey, maxTokens, temperature) => {
    // Extract chat ID from prompt if present to ensure each chat has its own cache entries
    let chatId = "default";
    if (prompt.startsWith('[Chat ID:')) {
      const endIndex = prompt.indexOf(']');
      if (endIndex > 0) {
        chatId = prompt.substring(9, endIndex).trim();
      }
    }
    return `${chatId}|${prompt}|${provider}|${domain}|${!!apiKey}|${maxTokens}|${temperature}`;
  },

  // Get a cached response if it exists and is recent (less than 30 minutes old)
  get: (key) => {
    if (apiCache.cache.has(key)) {
      const { timestamp, data } = apiCache.cache.get(key);
      const now = Date.now();
      const thirtyMinutes = 30 * 60 * 1000; // Increased from 5 to 30 minutes

      // Only use cache if it's less than 30 minutes old
      if (now - timestamp < thirtyMinutes) {
        // Don't log cache hits to reduce console clutter
        return data;
      }
    }
    return null;
  },

  // Store a response in the cache
  set: (key, data) => {
    // Don't cache error responses
    if (data && data.error) {
      console.log('Not caching error response');
      return;
    }

    apiCache.cache.set(key, {
      timestamp: Date.now(),
      data
    });

    // Limit cache size to 200 entries
    if (apiCache.cache.size > 200) {
      // Find the oldest entry
      let oldestKey = null;
      let oldestTime = Date.now();

      for (const [k, v] of apiCache.cache.entries()) {
        if (v.timestamp < oldestTime) {
          oldestTime = v.timestamp;
          oldestKey = k;
        }
      }

      if (oldestKey) {
        apiCache.cache.delete(oldestKey);
      }
    }
  }
};

/**
 * Generate text using the selected LLM provider
 *
 * @param {string} prompt - User prompt text
 * @param {string} provider - LLM provider ID
 * @param {string} domain - Domain ID for context
 * @param {string} apiKey - Provider API key
 * @param {number} maxTokens - Maximum tokens to generate
 * @param {number} temperature - Sampling temperature
 * @param {string} requestId - Optional unique ID for this specific request
 * @param {string} chatId - Optional chat ID this request belongs to
 * @returns {Promise} - Promise with generated text response
 */
export const generateText = async (prompt, provider, domain, apiKey, maxTokens = 500, temperature = 0.7, requestId = null, chatId = null) => {
  try {
    // For chat messages, we completely disable caching to ensure each chat gets a fresh response
    // This prevents confusion between different chats
    const isChatMessage = prompt.startsWith('[Chat ID:');

    // Extract chat ID from prompt if not provided
    if (!chatId && isChatMessage) {
      const endIndex = prompt.indexOf(']');
      if (endIndex > 0) {
        chatId = prompt.substring(9, endIndex).trim();
      }
    }

    // If chat ID is still not available, use a default
    if (!chatId) {
      chatId = "unknown";
    }

    // Generate a unique request ID if not provided
    if (!requestId) {
      requestId = Date.now().toString(36) + Math.random().toString(36).substring(2, 9);
    }

    // Only use cache for non-chat messages
    if (!isChatMessage) {
      // Check cache first to avoid duplicate requests
      const cacheKey = apiCache.getKey(prompt, provider, domain, apiKey, maxTokens, temperature);
      const cachedResponse = apiCache.get(cacheKey);

      if (cachedResponse) {
        return cachedResponse;
      }
    }

    // For OpenRouter, we can send an empty API key as the backend will use the environment variable
    // Make sure the request ID is included in the prompt
    let finalPrompt = prompt;
    if (!prompt.includes(`[Request ID: ${requestId}]`)) {
      // Add the request ID to the prompt if it's not already there
      finalPrompt = `${prompt}\n\n[Request ID: ${requestId}]`;
    }

    // Make a POST request to the backend API
    const response = await fetch(`${API_URL}/api/inference/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId, // Add request ID to headers for tracing
        'X-Chat-ID': chatId, // Add chat ID to headers for tracing
      },
      body: JSON.stringify({
        prompt: finalPrompt,
        provider,
        domain,
        api_key: apiKey || '', // Send empty string if no API key provided
        max_tokens: maxTokens,
        temperature,
        request_id: requestId, // Include in body as well for backend processing
        chat_id: chatId, // Include chat ID in body for backend processing
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to generate text');
    }

    const result = await response.json();

    // Add the request ID and chat ID to the result for tracking
    result.requestId = requestId;
    result.chatId = chatId;

    // Only cache non-chat messages
    if (!isChatMessage) {
      // Cache the successful response
      const cacheKey = apiCache.getKey(prompt, provider, domain, apiKey, maxTokens, temperature);
      apiCache.set(cacheKey, result);
    }

    return result;
  } catch (error) {
    // Don't log the full error to avoid exposing sensitive information
    return {
      text: `Error: ${error.message || 'Failed to generate text'}`,
      error: true,
      requestId: requestId,
      chatId: chatId
    };
  }
};

/**
 * Get list of available LLM providers
 *
 * @returns {Promise} - Promise with list of providers
 */
export const getProviders = async () => {
  try {
    const response = await fetch(`${API_URL}/api/inference/providers`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch providers');
    }

    return await response.json();
  } catch (error) {
    // Return empty array instead of throwing error
    return [];
  }
};

/**
 * Get list of available domains
 *
 * @returns {Promise} - Promise with list of domains
 */
export const getDomains = async () => {
  try {
    const response = await fetch(`${API_URL}/api/domain`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch domains');
    }

    return await response.json();
  } catch (error) {
    // Return empty array instead of throwing error
    return [];
  }
};

/**
 * Simple cache for title generation to prevent duplicate requests
 */
const titleCache = new Map();

/**
 * Generate a title for a conversation based on messages
 *
 * @param {Array} messages - Array of conversation messages
 * @param {string} provider - LLM provider ID
 * @param {string} apiKey - Provider API key
 * @returns {Promise} - Promise with generated title
 */
export const generateTitle = async (messages, provider, apiKey) => {
  try {
    // Create a simple cache key based on the first few messages
    const cacheKey = messages.slice(0, 3).map(m => `${m.sender}:${m.text.substring(0, 50)}`).join('|');

    // Check if we have a cached title
    if (titleCache.has(cacheKey)) {
      return titleCache.get(cacheKey);
    }

    // If we have more than 50 titles cached, remove the oldest ones
    if (titleCache.size > 50) {
      // Get the keys sorted by age (assuming they were added in order)
      const keys = Array.from(titleCache.keys());
      // Remove the 10 oldest keys
      for (let i = 0; i < 10 && i < keys.length; i++) {
        titleCache.delete(keys[i]);
      }
    }

    // If USE_SEPARATE_TITLE_KEY is enabled, use the backend endpoint for title generation
    if (USE_SEPARATE_TITLE_KEY && provider === 'openrouter') {
      try {
        // Call the backend endpoint for title generation
        const response = await fetch(`${API_URL}/api/inference/generate-title`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            messages: messages.slice(0, 3), // Only send first 3 messages
            provider: provider,
            domain: 'general'
          })
        });

        if (!response.ok) {
          throw new Error(`Title generation failed`);
        }

        const data = await response.json();

        if (data.title) {
          // Cache and return the generated title
          titleCache.set(cacheKey, data.title);
          return data.title;
        }

        // If no title was returned, silently fall back to client-side generation
      } catch (error) {
        // Silently continue with client-side generation
      }
    }

    // Client-side title generation as fallback
    // Try to generate a meaningful title based on the conversation context
    try {
      // Find all user messages
      const userMessages = messages.filter(msg => msg.sender === 'user');

      if (userMessages.length === 0) {
        return 'New Conversation';
      }

      // Get the first substantive user message (not just a greeting)
      const firstUserMessage = userMessages[0];
      const isGreeting = firstUserMessage.text.trim().toLowerCase().match(/^(hi|hello|hey|greetings|yo)$/);

      // If first message is just a greeting and we have more messages, use the second one
      const mainUserMessage = isGreeting && userMessages.length > 1 ? userMessages[1] : firstUserMessage;
      const text = mainUserMessage.text.trim();

      // If the message is short enough, just use it as the title
      if (text.length <= 40 && text.split(' ').length <= 8) {
        const simpleTitle = text;
        titleCache.set(cacheKey, simpleTitle);
        return simpleTitle;
      }

      // Try to extract a title from the first sentence if it's short
      const firstSentence = text.split(/[.!?]/, 1)[0].trim();
      if (firstSentence.length <= 40 && firstSentence.split(' ').length <= 8) {
        titleCache.set(cacheKey, firstSentence);
        return firstSentence;
      }

      // Try to identify key nouns and important words
      // This is a simple approach - look for capitalized words and longer words
      const words = text.split(/\s+/);

      // First try to find capitalized words (proper nouns, concepts, etc.)
      const capitalizedWords = words.filter(word =>
        word.length > 1 &&
        word[0] === word[0].toUpperCase() &&
        word[0] !== word[0].toLowerCase() &&
        !['I', 'A', 'An', 'The', 'In', 'On', 'At', 'For', 'With', 'By', 'To', 'And'].includes(word)
      );

      if (capitalizedWords.length >= 2 && capitalizedWords.length <= 5) {
        const keyTerms = capitalizedWords.join(' ');
        if (keyTerms.length <= 50) {
          titleCache.set(cacheKey, keyTerms);
          return keyTerms;
        }
      }

      // Next try to find important words (longer than 4 chars)
      const importantWords = words.filter(word => word.length > 4)
        .filter(word => !['about', 'after', 'again', 'below', 'could', 'every', 'first', 'found', 'great', 'house', 'large', 'learn', 'never', 'other', 'place', 'small', 'study', 'their', 'there', 'these', 'thing', 'think', 'three', 'water', 'where', 'which', 'world', 'would', 'write'].includes(word.toLowerCase()));

      if (importantWords.length >= 2) {
        const keyWords = importantWords.slice(0, 4).join(' ');
        if (keyWords.length <= 50) {
          titleCache.set(cacheKey, keyWords);
          return keyWords;
        }
      }

      // Last resort: Just use the first 30 chars of the message
      const shortTitle = text.substring(0, 30) + (text.length > 30 ? '...' : '');
      titleCache.set(cacheKey, shortTitle);
      return shortTitle;
    } catch (error) {
      // If anything fails in our improved logic, fall back to simplest approach
      const firstUserMessage = messages.find(msg => msg.sender === 'user');
      if (firstUserMessage) {
        const fallbackTitle = firstUserMessage.text.substring(0, 30) +
          (firstUserMessage.text.length > 30 ? '...' : '');
        return fallbackTitle;
      }
      return 'New Conversation';
    }

    // If we have no user messages, return a default title
    return 'New Conversation';
  } catch (error) {
    // Silently use fallback without logging error details

    // Fallback: Use the first user message as the title
    const firstUserMessage = messages.find(msg => msg.sender === 'user');
    if (firstUserMessage) {
      const fallbackTitle = firstUserMessage.text.substring(0, 30) +
        (firstUserMessage.text.length > 30 ? '...' : '');
      return fallbackTitle;
    }

    // Return a default title if generation fails
    return 'New Conversation';
  }
};

/**
 * Check if an API key is valid
 *
 * @param {string} provider - LLM provider ID
 * @param {string} apiKey - Provider API key
 * @returns {Promise<boolean>} - Promise with validation result
 */
export const validateApiKey = async (provider, apiKey) => {
  // For OpenRouter, just check if the API key has the correct format
  // This avoids making an unnecessary API call that counts against the rate limit
  if (provider === 'openrouter') {
    // Check if it's a valid-looking API key (starts with 'sk-' and has sufficient length)
    if (apiKey && apiKey.startsWith('sk-') && apiKey.length > 20) {
      return true;
    }
    return false;
  }

  try {
    // For other providers, use a minimal validation prompt
    const prompt = "Test";

    const response = await generateText(prompt, provider, 'general', apiKey, 5, 0.5);

    // If we get a response, the API key is valid
    return !!response.text;
  } catch (error) {
    // Silently return false without logging sensitive information
    return false;
  }
};

/**
 * Fine-tuning API functions
 */

/**
 * Get available fine-tuning parameters
 *
 * @returns {Promise} - Promise with fine-tuning parameters
 */
export const getFineTuningParameters = async () => {
  try {
    const response = await fetch(`${API_URL}/api/fine-tuning/parameters`);

    if (!response.ok) {
      throw new Error(`Failed to fetch fine-tuning parameters: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Start a fine-tuning job
 *
 * @param {Object} data - Fine-tuning job data
 * @returns {Promise} - Promise with job information
 */
export const startFineTuning = async (data) => {
  try {
    const response = await fetch(`${API_URL}/api/fine-tuning/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to start fine-tuning: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * Get status of a fine-tuning job
 *
 * @param {string} jobId - Fine-tuning job ID
 * @returns {Promise} - Promise with job status
 */
export const getFineTuningStatus = async (jobId) => {
  try {
    const response = await fetch(`${API_URL}/api/fine-tuning/status/${jobId}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to get job status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

/**
 * List fine-tuned models
 *
 * @returns {Promise} - Promise with list of fine-tuned models
 */
export const listFineTunedModels = async () => {
  try {
    const response = await fetch(`${API_URL}/api/fine-tuning/models`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to list fine-tuned models: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};