"""
VertoAI Backend Configuration
"""

import os
from dotenv import load_dotenv

# Load environment variables from the correct .env file
from pathlib import Path
env_path = Path(__file__).parent.parent / '.env'

# Load the environment variables
load_dotenv(env_path)

# Flask configuration
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
SECRET_KEY = os.getenv('SECRET_KEY', 'dev-key-change-in-production')
PORT = int(os.getenv('PORT', 5000))

# CORS configuration
CORS_ORIGINS = os.getenv('CORS_ORIGINS', 'http://localhost:3000,http://localhost:5173').split(',')

# API keys (these would be loaded from environment variables in production)
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY', '')
ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY', '')
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY', '')
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY', '')

# Add title generation API key
OPENROUTER_TITLE_API_KEY = os.getenv('OPENROUTER_TITLE_API_KEY', OPENROUTER_API_KEY)

# Rate limiting
RATE_LIMIT_REQUESTS = int(os.getenv('RATE_LIMIT_REQUESTS', 500))
RATE_LIMIT_PERIOD = int(os.getenv('RATE_LIMIT_PERIOD', 86400))  # 24 hours in seconds

# Fine-tuning configuration
FINE_TUNING_ENABLED = os.getenv('FINE_TUNING_ENABLED', 'True').lower() == 'true'
MAX_CONCURRENT_JOBS = int(os.getenv('MAX_CONCURRENT_JOBS', 2))
MODEL_STORAGE_PATH = os.getenv('MODEL_STORAGE_PATH', './models')

# LangChain configuration
LANGCHAIN_TRACING = os.getenv('LANGCHAIN_TRACING', 'False').lower() == 'true'
LANGCHAIN_PROJECT = os.getenv('LANGCHAIN_PROJECT', 'vertoai')
