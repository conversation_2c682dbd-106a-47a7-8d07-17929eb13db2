import { useState, useEffect, useRef, useCallback } from 'react'
import './App.css'
import DomainSelector from './components/DomainSelector'
import ProviderSelector from './components/ProviderSelector'
import ChatWindow from './components/ChatWindow'
import ThemeSelector from './components/ThemeSelector'
import ApiKeyModal from './components/ApiKeyModal'
import TutorialModal from './components/TutorialModal'
import ThreeBackground from './components/ThreeBackground'
import HistoryPanel from './components/HistoryPanel'
import DeleteConfirmationModal from './components/DeleteConfirmationModal'
import ArchiveConfirmationModal from './components/ArchiveConfirmationModal'
import FineTuningTab from './components/FineTuningTab'
import { generateTitle } from './services/api'
import { DOMAINS, PROVIDERS } from './utils/config'

function App() {
  const [domain, setDomain] = useState(DOMAINS[0]?.id || 'fintech')
  // Find the default provider (OpenRouter) or fallback to the first provider
  const defaultProvider = PROVIDERS.find(p => p.defaultForFree)?.id || PROVIDERS[0]?.id || 'openrouter'
  const [provider, setProvider] = useState(defaultProvider)
  const [apiKey, setApiKey] = useState('')
  const [theme, setTheme] = useState('dark')
  const [showApiKeyModal, setShowApiKeyModal] = useState(false)
  const [showTutorialModal, setShowTutorialModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showArchiveModal, setShowArchiveModal] = useState(false)
  const [showDeleteAllModal, setShowDeleteAllModal] = useState(false)
  const [chatToDelete, setChatToDelete] = useState(null)
  const [chatToArchive, setChatToArchive] = useState(null)

  // Active tab state (chat or fine-tuning)
  const [activeTab, setActiveTab] = useState('chat')

  // Chat history state
  const [chatHistory, setChatHistory] = useState([])
  const [archivedChats, setArchivedChats] = useState([])
  const [currentChatId, setCurrentChatId] = useState(null)

  // Generate a unique ID for chats
  const generateChatId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5)
  }

  // Create a new chat
  const handleNewChat = useCallback(() => {
    console.log("Creating new chat...");

    // Generate a new unique ID for the chat
    const newChatId = generateChatId();

    // Create a completely fresh chat object with empty messages
    const newChat = {
      id: newChatId,
      title: 'New Conversation',
      lastMessage: '',
      date: new Date(),
      isPinned: false,
      titleGenerated: false, // Track if we've generated a title
      messages: [], // Ensure messages array is empty
      domain: domain, // Store the current domain with the chat
      hasUnread: false // Track unread messages
    };

    // Add the new chat to the beginning of the history
    setChatHistory(prev => [newChat, ...prev]);

    // Set the current chat ID to the new chat
    setCurrentChatId(newChatId);

    // Force the ChatWindow to reset
    const chatWindowElement = document.querySelector('.chat-messages');
    if (chatWindowElement) {
      chatWindowElement.scrollTop = 0;
    }

    // Clear any input in the chat window
    const chatInput = document.querySelector('.chat-input');
    if (chatInput) {
      chatInput.value = '';
    }

    console.log("New chat created with ID:", newChatId);
  }, [domain])

  // Initialize with a new chat if none exists, but only once on initial load
  // Using a ref to ensure this only runs once, even if chatHistory changes
  const initializedRef = useRef(false);

  useEffect(() => {
    // Only create a new chat if we haven't initialized yet
    // This prevents creating multiple chats on page reload
    if (!initializedRef.current) {
      initializedRef.current = true;

      // Clear any existing chats to ensure we start with exactly one
      setChatHistory([]);

      // Create only one chat - using setTimeout to avoid dependency issues
      // This ensures we don't need to add handleNewChat to the dependency array
      setTimeout(() => {
        handleNewChat();
      }, 0);
    }
  }, []) // Empty dependency array ensures this only runs once on component mount

  // Handle selecting a chat
  const handleSelectChat = (chatId) => {
    setCurrentChatId(chatId)

    // Clear unread flag when selecting a chat
    setChatHistory(prev =>
      prev.map(chat =>
        chat.id === chatId
          ? { ...chat, hasUnread: false }
          : chat
      )
    )
  }

  // Handle archiving a chat
  const handleArchiveChat = (chatId) => {
    const chatToArchive = chatHistory.find(chat => chat.id === chatId)
    if (chatToArchive) {
      // If the chat is pinned, unpin it before archiving
      const chatToMove = chatToArchive.isPinned
        ? { ...chatToArchive, isPinned: false }
        : chatToArchive;

      // Remove from chat history and add to archived chats
      setChatHistory(prev => prev.filter(chat => chat.id !== chatId))
      setArchivedChats(prev => [chatToMove, ...prev])

      // If the archived chat was the current one, select another chat or create a new one
      if (currentChatId === chatId) {
        if (chatHistory.length > 1) {
          // Find the next chat to select
          const nextChat = chatHistory.find(chat => chat.id !== chatId)
          if (nextChat) {
            setCurrentChatId(nextChat.id)
          }
        } else {
          // Create a new chat if this was the last one
          handleNewChat()
        }
      }
    }
  }

  // Handle unarchiving a chat
  const handleUnarchiveChat = (chatId) => {
    const chatToUnarchive = archivedChats.find(chat => chat.id === chatId)
    if (chatToUnarchive) {
      setArchivedChats(prev => prev.filter(chat => chat.id !== chatId))
      setChatHistory(prev => [chatToUnarchive, ...prev])
      setCurrentChatId(chatId)
    }
  }

  // Handle deleting a chat
  const handleDeleteChat = (chatId) => {
    // Check if it's in the regular history
    if (chatHistory.some(chat => chat.id === chatId)) {
      setChatHistory(prev => prev.filter(chat => chat.id !== chatId))

      // If the deleted chat was the current one, select another chat or create a new one
      if (currentChatId === chatId) {
        if (chatHistory.length > 1) {
          // Find the next chat to select
          const nextChat = chatHistory.find(chat => chat.id !== chatId)
          if (nextChat) {
            setCurrentChatId(nextChat.id)
          }
        } else {
          // Create a new chat if this was the last one
          handleNewChat()
        }
      }
    }
    // Check if it's in the archived chats
    else if (archivedChats.some(chat => chat.id === chatId)) {
      setArchivedChats(prev => prev.filter(chat => chat.id !== chatId))
    }
  }

  // Handle pinning/unpinning a chat
  const handlePinChat = (chatId) => {
    setChatHistory(prev =>
      prev.map(chat =>
        chat.id === chatId
          ? { ...chat, isPinned: !chat.isPinned }
          : chat
      )
    )
  }

  // Update chat with new messages
  const handleUpdateChat = async (chatId, messages, title) => {
    // Check if the chat exists in history
    const chatExists = chatHistory.some(chat => chat.id === chatId);

    // If the chat doesn't exist (which can happen if it was deleted),
    // we should ignore the update
    if (!chatExists) {
      console.log(`Ignoring update for non-existent chat: ${chatId}`);
      return;
    }

    // Generate a title if there are messages but no title
    let chatTitle = title;
    let titleIsNew = false;

    if (messages.length > 0 && (!title || title === 'New Conversation')) {
      try {
        // Only generate a title if we have at least 2 messages (including both user and AI)
        // This ensures we have enough context for a meaningful title
        const chat = chatHistory.find(c => c.id === chatId);
        // Only generate a title once when we have enough messages and don't already have a custom title
        const shouldGenerateTitle = messages.length >= 2 &&
                                   (!chat || chat.title === 'New Conversation') &&
                                   !chat?.titleGenerated; // Track if we've already generated a title

        if (shouldGenerateTitle) {
          // Get the first 3 messages for context (should include at least one user and one AI message)
          const firstThreeMessages = messages.slice(0, 3);

          // Check if we have both user and AI messages for better context
          const hasUserMessage = firstThreeMessages.some(msg => msg.sender === 'user');
          const hasAIMessage = firstThreeMessages.some(msg => msg.sender === 'ai');

          if (hasUserMessage && hasAIMessage) {
            try {
              // Generate title based on the first 3 messages
              const generatedTitle = await generateTitle(
                firstThreeMessages,
                provider,
                apiKey
              );

              if (generatedTitle && generatedTitle !== 'New Conversation') {
                chatTitle = generatedTitle;
                titleIsNew = true; // Mark this title as new for animation
              }
            } catch (titleError) {
              // Silently fail and use fallback
            }
          }
        }

        // Fallback: If we still don't have a title, try to generate a better one
        if (!chatTitle || chatTitle === 'New Conversation') {
          // Find all user messages
          const userMessages = messages.filter(msg => msg.sender === 'user');

          if (userMessages.length > 0) {
            // Skip greetings for title generation
            const isGreeting = (text) => {
              const lowerText = text.toLowerCase().trim();
              return ['hi', 'hello', 'hey', 'greetings', 'hi there', 'hello there'].includes(lowerText);
            };

            // Find the first substantive message (not just a greeting)
            let titleMessage = userMessages[0];
            if (userMessages.length > 1 && isGreeting(userMessages[0].text)) {
              titleMessage = userMessages[1];
            }

            // Extract a meaningful title
            const text = titleMessage.text.trim();

            // Look for question marks to identify the main question
            if (text.includes('?')) {
              const questions = text.split('?');
              const mainQuestion = questions[0] + '?';
              if (mainQuestion.length <= 50) {
                chatTitle = mainQuestion;
              } else {
                chatTitle = mainQuestion.substring(0, 47) + '...';
              }
            } else {
              // If no question, use the first sentence or part of it
              const firstSentence = text.split(/[.!]/, 1)[0].trim();
              if (firstSentence.length <= 50) {
                chatTitle = firstSentence;
              } else {
                chatTitle = firstSentence.substring(0, 47) + '...';
              }
            }
          }
        }
      } catch (error) {
        // Silently keep existing title or use default
      }
    }

    setChatHistory(prev => {
      // Check if the chat still exists in the history
      // This is important because the chat might have been deleted while we were generating the title
      if (!prev.some(chat => chat.id === chatId)) {
        console.log(`Chat ${chatId} no longer exists, ignoring update`);
        return prev;
      }

      return prev.map(chat => {
        if (chat.id === chatId) {
          // Get the last message for display in the chat list
          const lastMessage = messages.length > 0
            ? messages[messages.length - 1].text.substring(0, 50) + (messages[messages.length - 1].text.length > 50 ? '...' : '')
            : '';

          // Mark chat as having unread messages if it's not the current chat
          // and the last message is from the AI (to avoid marking user's own messages as unread)
          const hasNewAiMessage =
            messages.length > 0 &&
            messages[messages.length - 1].sender === 'ai' &&
            currentChatId !== chatId;

          // Check if the last message has a specific chatId property
          // This helps ensure responses go to the correct chat
          const lastMsg = messages[messages.length - 1];
          if (lastMsg && lastMsg.chatId && lastMsg.chatId !== chatId) {
            console.log(`Message chatId mismatch: message=${lastMsg.chatId}, chat=${chatId}`);
            // If the message has a different chatId, it belongs to another chat
            // We should not update this chat with it
            return chat;
          }

          return {
            ...chat,
            messages,
            lastMessage,
            title: chatTitle || chat.title,
            titleIsNew: titleIsNew, // Add flag for typewriter animation
            titleGenerated: true, // Mark that we've generated a title for this chat
            date: new Date(),
            domain: chat.domain || domain, // Ensure domain is stored with the chat
            hasUnread: hasNewAiMessage ? true : chat.hasUnread
          };
        }
        return chat;
      });
    });
  }

  // Reset scroll position on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Text scramble glitch effect for app title
  useEffect(() => {
    const scrambleText = (element, originalText) => {
      const chars = '█▓▒░!@#$%^&*()_+-=[]{}|;:,.<>?ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      const glitchChars = '█▓▒░▄▀■□▪▫◆◇○●◊◈';
      let iteration = 0;
      const maxIterations = originalText.length + 35; // Even more iterations for extensive scrambling

      // Debug: Log when each character should start revealing
      console.log('Scramble Debug:');
      originalText.split('').forEach((char, index) => {
        const baseRevealTime = Math.floor(maxIterations * 0.75);
        const revealTime = baseRevealTime + (index * 1);
        console.log(`Character '${char}' (index ${index}) will reveal at iteration ${revealTime}`);
      });
      console.log(`Total iterations: ${maxIterations}`);

      // Add glitch styling and temporarily disable gradient
      element.style.textShadow = '0 0 10px #ff0040, 0 0 20px #00ffff, 0 0 30px rgba(var(--gradient-start-rgb), 0.8)';
      element.style.filter = 'hue-rotate(0deg) brightness(1.2)';
      element.style.webkitTextFillColor = '#ffffff'; // Override gradient during scrambling
      element.style.background = 'none'; // Disable gradient background

      const interval = setInterval(() => {
        element.textContent = originalText
          .split('')
          .map((char, index) => {
            // Calculate when this character should start revealing
            // Make sure V (index 0) scrambles for a LONG time
            const baseRevealTime = Math.floor(maxIterations * 0.75); // 75% through animation
            const revealStartTime = baseRevealTime + (index * 1);

            // FORCE ALL characters to scramble until their reveal time
            if (iteration <= revealStartTime) {
              // Always use random characters - no exceptions
              const useGlitchChar = Math.random() < 0.5;
              const charSet = useGlitchChar ? glitchChars : chars;
              return charSet[Math.floor(Math.random() * charSet.length)];
            }
            return originalText[index];
          })
          .join('');

        // Add random color shifts during scrambling
        if (iteration < maxIterations - 5) {
          const hueShift = Math.random() * 360;
          element.style.filter = `hue-rotate(${hueShift}deg) brightness(${1.2 + Math.random() * 0.3})`;
        }

        if (iteration >= maxIterations) {
          clearInterval(interval);
          // Reset to normal styling by removing inline styles and letting CSS take over
          element.style.textShadow = '';
          element.style.filter = '';
          element.style.webkitTextFillColor = '';
          element.style.background = '';
          element.style.backgroundSize = '';
          element.style.webkitBackgroundClip = '';
          element.style.backgroundClip = '';
          element.textContent = originalText;
        }

        iteration += 1;
      }, 40); // Faster for more intense scrambling
    };

    const triggerGlitch = () => {
      const titleElement = document.querySelector('.app-title');
      if (titleElement) {
        const originalText = 'VERTOAI';
        scrambleText(titleElement, originalText);
      }
    };

    // Trigger glitch effect every 25 seconds
    const glitchInterval = setInterval(triggerGlitch, 25000);

    // Also trigger once after 3 seconds for initial demo
    const initialTimeout = setTimeout(triggerGlitch, 3000);

    // Cleanup interval on component unmount
    return () => {
      clearInterval(glitchInterval);
      clearTimeout(initialTimeout);
    };
  }, []);

  // Apply theme
  useEffect(() => {
    // Update CSS variables based on theme
    const root = document.documentElement;

    // Set theme attribute on body for theme-specific CSS
    document.body.setAttribute('data-theme', theme);

    if (theme === 'light') {
      root.style.setProperty('--bg-color', 'var(--light-bg)');
      root.style.setProperty('--surface-color', 'var(--light-surface)');
      root.style.setProperty('--surface-color-2', 'var(--light-surface-2)');
      root.style.setProperty('--text-color', 'var(--light-text)');
      root.style.setProperty('--text-color-secondary', 'var(--light-text-secondary)');
      root.style.setProperty('--border-color', 'var(--light-border)');
      root.style.setProperty('--gradient-start', '#4a6cf7');
      root.style.setProperty('--gradient-mid', '#6e48e6');
      root.style.setProperty('--gradient-end', '#9333ea');
    } else if (theme === 'cosmic') {
      root.style.setProperty('--bg-color', '#0f0c29');
      root.style.setProperty('--surface-color', '#1a1745');
      root.style.setProperty('--surface-color-2', '#24243e');
      root.style.setProperty('--text-color', '#e6e6fa');
      root.style.setProperty('--text-color-secondary', '#b8b8e6');
      root.style.setProperty('--border-color', '#302b63');
      root.style.setProperty('--gradient-start', '#7303c0');
      root.style.setProperty('--gradient-mid', '#ec38bc');
      root.style.setProperty('--gradient-end', '#fdeff9');
    } else if (theme === 'aurora') {
      root.style.setProperty('--bg-color', '#0a192f');
      root.style.setProperty('--surface-color', '#112240');
      root.style.setProperty('--surface-color-2', '#1d3557');
      root.style.setProperty('--text-color', '#e6f1ff');
      root.style.setProperty('--text-color-secondary', '#a8b2d1');
      root.style.setProperty('--border-color', '#233554');
      root.style.setProperty('--gradient-start', '#5390d9');
      root.style.setProperty('--gradient-mid', '#4ea8de');
      root.style.setProperty('--gradient-end', '#48bfe3');
    } else if (theme === 'nebula') {
      root.style.setProperty('--bg-color', '#13111C');
      root.style.setProperty('--surface-color', '#1E1B2E');
      root.style.setProperty('--surface-color-2', '#2D2B40');
      root.style.setProperty('--text-color', '#E2E1E6');
      root.style.setProperty('--text-color-secondary', '#A09FA6');
      root.style.setProperty('--border-color', '#2D2B40');
      root.style.setProperty('--gradient-start', '#9333ea');
      root.style.setProperty('--gradient-mid', '#a855f7');
      root.style.setProperty('--gradient-end', '#c084fc');
    } else if (theme === 'quantum') {
      root.style.setProperty('--bg-color', '#0f1c1a');
      root.style.setProperty('--surface-color', '#1a2e2a');
      root.style.setProperty('--surface-color-2', '#2a3c39');
      root.style.setProperty('--text-color', '#e0e7e5');
      root.style.setProperty('--text-color-secondary', '#a3b5b0');
      root.style.setProperty('--border-color', '#2a3c39');
      root.style.setProperty('--gradient-start', '#059669');
      root.style.setProperty('--gradient-mid', '#10b981');
      root.style.setProperty('--gradient-end', '#34d399');
    } else if (theme === 'synthwave') {
      root.style.setProperty('--bg-color', '#1a1a2e');
      root.style.setProperty('--surface-color', '#16213e');
      root.style.setProperty('--surface-color-2', '#1f2b54');
      root.style.setProperty('--text-color', '#ffffff');
      root.style.setProperty('--text-color-secondary', '#c8c8e0');
      root.style.setProperty('--border-color', '#2a3a6a');
      root.style.setProperty('--gradient-start', '#fc28a8');
      root.style.setProperty('--gradient-mid', '#7638fa');
      root.style.setProperty('--gradient-end', '#3d30fa');
    } else {
      // Default dark theme
      root.style.setProperty('--bg-color', 'var(--dark-bg)');
      root.style.setProperty('--surface-color', 'var(--dark-surface)');
      root.style.setProperty('--surface-color-2', 'var(--dark-surface-2)');
      root.style.setProperty('--text-color', 'var(--dark-text)');
      root.style.setProperty('--text-color-secondary', 'var(--dark-text-secondary)');
      root.style.setProperty('--border-color', 'var(--dark-border)');
      root.style.setProperty('--gradient-start', '#4a6cf7');
      root.style.setProperty('--gradient-mid', '#6e48e6');
      root.style.setProperty('--gradient-end', '#9333ea');
    }
  }, [theme]);

  const handleDomainChange = (newDomain) => {
    // Just change the domain without creating a new chat
    setDomain(newDomain)

    // If there's no current chat, create a new one
    if (!currentChatId || chatHistory.length === 0) {
      handleNewChat()
    } else {
      // Force the ChatWindow to reset
      const chatWindowElement = document.querySelector('.chat-messages')
      if (chatWindowElement) {
        chatWindowElement.scrollTop = 0
      }
    }
  }

  const handleProviderChange = (newProvider) => {
    setProvider(newProvider)
  }

  const handleApiKeyChange = (newApiKey) => {
    setApiKey(newApiKey)
  }

  const handleThemeChange = (newTheme) => {
    setTheme(newTheme)
  }

  // Handle showing delete modal
  const handleShowDeleteModal = (chat) => {
    setChatToDelete(chat)
    setShowDeleteModal(true)
  }

  // Handle confirming delete
  const confirmDelete = () => {
    if (chatToDelete) {
      handleDeleteChat(chatToDelete.id)
      setChatToDelete(null)
    }
  }

  // Handle showing archive modal
  const handleShowArchiveModal = (chat) => {
    setChatToArchive(chat)
    setShowArchiveModal(true)
  }

  // Handle confirming archive
  const confirmArchive = () => {
    if (chatToArchive) {
      handleArchiveChat(chatToArchive.id)
      setChatToArchive(null)
    }
  }

  // Handle showing delete all chats modal
  const handleShowDeleteAllModal = () => {
    setShowDeleteAllModal(true)
  }

  // Handle confirming delete all chats
  const confirmDeleteAllChats = () => {
    // Clear chat history and archived chats
    setChatHistory([])
    setArchivedChats([])
    // Create a new chat
    handleNewChat()
    // Close the modal
    setShowDeleteAllModal(false)
  }

  return (
    <>
      {/* Three.js background */}
      <ThreeBackground theme={theme} />

      <div className="app-container">
        {/* Background is visible through the semi-transparent container */}
        {/* Theme selector */}
        <ThemeSelector
          currentTheme={theme}
          onThemeChange={handleThemeChange}
        />

        {/* API Key button */}
        <div
          className="api-key-button"
          onClick={() => setShowApiKeyModal(true)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
          </svg>
          <div className="api-key-tooltip">Set API Key</div>
        </div>

        {/* Help button */}
        <button
          className="help-button"
          onClick={() => setShowTutorialModal(true)}
        >
          ?
          <div className="help-tooltip">Help & Support</div>
        </button>

        <header className="app-header">
          <h1 className="app-title" data-text="VertoAI">VertoAI</h1>
          <p className="app-subtitle">Multi-domain Gen AI Platform</p>
        </header>

        <div className="tab-selector">
          <button
            className={`tab-button ${activeTab === 'chat' ? 'active' : ''}`}
            onClick={() => setActiveTab('chat')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
            Chat
          </button>
          <button
            className={`tab-button ${activeTab === 'fine-tuning' ? 'active' : ''}`}
            onClick={() => setActiveTab('fine-tuning')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ marginRight: '8px' }}>
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
            </svg>
            Fine-Tuning
          </button>
        </div>

        <main className="app-main">
          {activeTab === 'chat' ? (
            <>
              <div className="app-layout">
                <HistoryPanel
                  chatHistory={chatHistory}
                  archivedChats={archivedChats}
                  onSelectChat={handleSelectChat}
                  onNewChat={handleNewChat}
                  onArchiveChat={handleArchiveChat}
                  onUnarchiveChat={handleUnarchiveChat}
                  onDeleteChat={handleDeleteChat}
                  onPinChat={handlePinChat}
                  currentChatId={currentChatId}
                  onShowDeleteModal={handleShowDeleteModal}
                  onShowArchiveModal={handleShowArchiveModal}
                  onDeleteAllChats={handleShowDeleteAllModal}
                  selectedDomain={domain}
                />

                <div className="chat-content">
                  <ChatWindow
                    selectedDomain={domain}
                    selectedProvider={provider}
                    apiKey={apiKey}
                    chatId={currentChatId}
                    onUpdateChat={handleUpdateChat}
                    onArchiveChat={() => currentChatId && handleArchiveChat(currentChatId)}
                    onDeleteChat={() => currentChatId && handleDeleteChat(currentChatId)}
                    onPinChat={() => currentChatId && handlePinChat(currentChatId)}
                    isPinned={chatHistory.find(chat => chat.id === currentChatId)?.isPinned || false}
                    initialMessages={chatHistory.find(chat => chat.id === currentChatId)?.messages || []}
                  />
                </div>
              </div>

              <div className="controls-container">
                <DomainSelector
                  selectedDomain={domain}
                  onDomainChange={handleDomainChange}
                />

                <ProviderSelector
                  selectedProvider={provider}
                  onProviderChange={handleProviderChange}
                />
              </div>
            </>
          ) : (
            <FineTuningTab
              selectedDomain={domain}
              selectedProvider={provider}
              apiKey={apiKey}
            />
          )}
        </main>

        <footer className="app-footer">
          <p>VertoAI - Open Source Multi-domain Gen AI Platform</p>
        </footer>

        {/* Modals */}
        <ApiKeyModal
          isOpen={showApiKeyModal}
          onClose={() => setShowApiKeyModal(false)}
          onSave={handleApiKeyChange}
          selectedProvider={provider}
          currentApiKey={apiKey}
          onProviderChange={handleProviderChange}
        />

        <TutorialModal
          isOpen={showTutorialModal}
          onClose={() => setShowTutorialModal(false)}
        />

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDelete}
          chatTitle={chatToDelete?.title || 'this chat'}
        />

        {/* Archive Confirmation Modal */}
        <ArchiveConfirmationModal
          isOpen={showArchiveModal}
          onClose={() => setShowArchiveModal(false)}
          onConfirm={confirmArchive}
          chatTitle={chatToArchive?.title || 'this chat'}
          isPinned={chatToArchive?.isPinned || false}
        />

        {/* Delete All Chats Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={showDeleteAllModal}
          onClose={() => setShowDeleteAllModal(false)}
          onConfirm={confirmDeleteAllChats}
          title="Delete All Chats"
          message="Are you sure you want to delete all chats? This action cannot be undone and will remove all your conversations."
          confirmText="Delete All"
          type="danger"
        />
      </div>
    </>
  )
}

export default App
