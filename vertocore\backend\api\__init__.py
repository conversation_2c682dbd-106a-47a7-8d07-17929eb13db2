"""
VertoAI Backend API
Flask application initialization
"""

from flask import Flask
from flask_cors import CORS

def create_app(config=None):
    """
    Create and configure the Flask application

    Args:
        config: Configuration object or path to config file

    Returns:
        Flask application instance
    """
    app = Flask(__name__)

    # Enable CORS for frontend
    CORS(app, resources={r"/*": {"origins": "*"}})

    # Load configuration
    if config:
        app.config.from_object(config)
    else:
        # Default configuration
        from pathlib import Path
        config_path = Path(__file__).parent.parent / 'config.py'
        app.config.from_pyfile(str(config_path))

    # Register blueprints
    from .routes import domain_bp, inference_bp, fine_tuning_bp

    app.register_blueprint(domain_bp)
    app.register_blueprint(inference_bp)
    app.register_blueprint(fine_tuning_bp)

    # Register error handlers
    @app.errorhandler(404)
    def not_found(error):
        return {"error": "Resource not found"}, 404

    @app.errorhandler(500)
    def server_error(error):
        return {"error": "Internal server error"}, 500

    # Health check endpoint
    @app.route('/health')
    def health_check():
        return {"status": "healthy"}, 200

    return app
