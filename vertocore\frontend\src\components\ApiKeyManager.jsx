import React, { useState } from 'react';

const ApiKeyManager = ({ onApi<PERSON><PERSON><PERSON><PERSON><PERSON>, selectedProvider }) => {
  const [apiKey, setApiKey] = useState('');
  const [showKey, setShowKey] = useState(false);

  const handleChange = (e) => {
    const newKey = e.target.value;
    setApiKey(newKey);
    onApiKeyChange(newKey);
  };

  const toggleShowKey = () => {
    setShowKey(!showKey);
  };

  // Get provider-specific placeholder
  const getPlaceholder = () => {
    switch (selectedProvider) {
      case 'openai':
        return 'Enter OpenAI API key (starts with sk-)';
      case 'gemini':
        return 'Enter Google Gemini API key';
      case 'claude':
        return 'Enter Anthropic Claude API key';
      case 'openrouter':
        return 'Enter OpenRouter API key';
      case 'deepseek':
        return 'Enter DeepSeek API key';
      default:
        return 'Enter your API key (not stored)';
    }
  };

  return (
    <div className="control-section">
      <h2>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
        </svg>
        API Key
      </h2>
      <div className="api-key-container">
        <input
          type={showKey ? "text" : "password"}
          placeholder={getPlaceholder()}
          className="api-key-input"
          value={apiKey}
          onChange={handleChange}
        />
        <button
          className="api-key-toggle"
          onClick={toggleShowKey}
          title={showKey ? "Hide API key" : "Show API key"}
        >
          {showKey ? "🙈" : "👁️"}
        </button>
      </div>
      <p className="api-key-note">Your API key is never stored and only passed through to the provider.</p>
    </div>
  );
};

export default ApiKeyManager;
