import React, { useState, useEffect } from 'react';
import { getFineTuningParameters, startFineTuning, getFineTuningStatus, listFineTunedModels } from '../services/api';
import FineTuningTutorial from './FineTuningTutorial';

const FineTuningTab = ({ selectedDomain, selectedProvider, apiKey }) => {
  // State for fine-tuning parameters
  const [parameters, setParameters] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // State for form inputs
  const [baseModel, setBaseModel] = useState('');
  const [dataset, setDataset] = useState('');
  const [loraRank, setLoraRank] = useState(64);
  const [loraAlpha, setLoraAlpha] = useState(16);
  const [loraDropout, setLoraDropout] = useState(0.1);
  const [learningRate, setLearningRate] = useState(3e-4);
  const [batchSize, setBatchSize] = useState(4);
  const [numEpochs, setNumEpochs] = useState(3);
  const [quantization, setQuantization] = useState('4-bit');
  
  // State for job status
  const [jobId, setJobId] = useState(null);
  const [jobStatus, setJobStatus] = useState(null);
  const [statusPolling, setStatusPolling] = useState(null);
  
  // State for fine-tuned models
  const [fineTunedModels, setFineTunedModels] = useState([]);
  
  // State for tutorial
  const [showTutorial, setShowTutorial] = useState(false);
  
  // State for active tab
  const [activeTab, setActiveTab] = useState('new');

  // Fetch parameters on component mount
  useEffect(() => {
    const fetchParameters = async () => {
      try {
        setLoading(true);
        const data = await getFineTuningParameters();
        setParameters(data);
        
        // Set default values
        if (data.base_models && data.base_models.length > 0) {
          setBaseModel(data.base_models[0].id);
        }
        
        setLoading(false);
      } catch (err) {
        setError('Failed to load fine-tuning parameters');
        setLoading(false);
      }
    };
    
    fetchParameters();
    
    // Fetch fine-tuned models
    const fetchModels = async () => {
      try {
        const models = await listFineTunedModels();
        setFineTunedModels(models);
      } catch (err) {
        console.error('Failed to load fine-tuned models:', err);
      }
    };
    
    fetchModels();
    
    // Cleanup polling on unmount
    return () => {
      if (statusPolling) {
        clearInterval(statusPolling);
      }
    };
  }, []);
  
  // Update dataset options when domain changes
  useEffect(() => {
    if (parameters && parameters.datasets && parameters.datasets[selectedDomain]) {
      const domainDatasets = parameters.datasets[selectedDomain];
      if (domainDatasets.length > 0) {
        setDataset(domainDatasets[0].id);
      } else {
        setDataset('');
      }
    }
  }, [selectedDomain, parameters]);
  
  // Poll job status when job is running
  useEffect(() => {
    if (jobId && !statusPolling) {
      const pollStatus = async () => {
        try {
          const status = await getFineTuningStatus(jobId);
          setJobStatus(status);
          
          // Stop polling when job is completed or failed
          if (status.status === 'completed' || status.status === 'failed') {
            clearInterval(statusPolling);
            setStatusPolling(null);
            
            // Refresh fine-tuned models list if job completed
            if (status.status === 'completed') {
              const models = await listFineTunedModels();
              setFineTunedModels(models);
            }
          }
        } catch (err) {
          console.error('Failed to get job status:', err);
        }
      };
      
      // Poll immediately and then every 5 seconds
      pollStatus();
      const interval = setInterval(pollStatus, 5000);
      setStatusPolling(interval);
      
      return () => {
        clearInterval(interval);
      };
    }
  }, [jobId, statusPolling]);
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!apiKey) {
      setError('Please set your API key first');
      return;
    }
    
    try {
      const result = await startFineTuning({
        base_model: baseModel,
        domain: selectedDomain,
        dataset: dataset,
        api_key: apiKey,
        parameters: {
          lora_rank: loraRank,
          lora_alpha: loraAlpha,
          lora_dropout: loraDropout,
          learning_rate: learningRate,
          batch_size: batchSize,
          num_epochs: numEpochs,
          quantization: quantization
        }
      });
      
      setJobId(result.job_id);
      setJobStatus({
        status: result.status,
        progress: 0,
        message: result.message
      });
      
      // Switch to status tab
      setActiveTab('status');
    } catch (err) {
      setError('Failed to start fine-tuning: ' + (err.message || 'Unknown error'));
    }
  };
  
  // Reset form
  const handleReset = () => {
    if (parameters) {
      // Reset to defaults
      if (parameters.base_models && parameters.base_models.length > 0) {
        setBaseModel(parameters.base_models[0].id);
      }
      
      if (parameters.lora_parameters) {
        setLoraRank(parameters.lora_parameters.lora_rank.default);
        setLoraAlpha(parameters.lora_parameters.lora_alpha.default);
        setLoraDropout(parameters.lora_parameters.lora_dropout.default);
      }
      
      if (parameters.training_parameters) {
        setLearningRate(parameters.training_parameters.learning_rate.default);
        setBatchSize(parameters.training_parameters.batch_size.default);
        setNumEpochs(parameters.training_parameters.num_epochs.default);
      }
      
      if (parameters.quantization) {
        setQuantization(parameters.quantization.default);
      }
    }
    
    setError(null);
  };
  
  // Render loading state
  if (loading) {
    return (
      <div className="fine-tuning-container">
        <div className="fine-tuning-loading">
          <div className="loading-spinner"></div>
          <p>Loading fine-tuning parameters...</p>
        </div>
      </div>
    );
  }
  
  // Render error state
  if (error && !parameters) {
    return (
      <div className="fine-tuning-container">
        <div className="fine-tuning-error">
          <h3>Error</h3>
          <p>{error}</p>
          <button 
            className="primary-button"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  // Get available datasets for selected domain
  const availableDatasets = parameters?.datasets?.[selectedDomain] || [];
  
  return (
    <div className="fine-tuning-container">
      <div className="fine-tuning-header">
        <h2>Model Fine-Tuning</h2>
        <p className="fine-tuning-description">
          Fine-tune large language models for your specific domain using LoRA/QLoRA techniques.
        </p>
        
        <button 
          className="tutorial-button"
          onClick={() => setShowTutorial(true)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
          How Fine-Tuning Works
        </button>
      </div>
      
      <div className="fine-tuning-tabs">
        <button 
          className={`fine-tuning-tab ${activeTab === 'new' ? 'active' : ''}`}
          onClick={() => setActiveTab('new')}
        >
          New Fine-Tuning Job
        </button>
        <button 
          className={`fine-tuning-tab ${activeTab === 'status' ? 'active' : ''}`}
          onClick={() => setActiveTab('status')}
        >
          Job Status
        </button>
        <button 
          className={`fine-tuning-tab ${activeTab === 'models' ? 'active' : ''}`}
          onClick={() => setActiveTab('models')}
        >
          Fine-Tuned Models
        </button>
      </div>
      
      <div className="fine-tuning-content">
        {activeTab === 'new' && (
          <form className="fine-tuning-form" onSubmit={handleSubmit}>
            {error && (
              <div className="form-error">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <span>{error}</span>
              </div>
            )}
            
            <div className="form-section">
              <h3>Model & Dataset</h3>
              
              <div className="form-group">
                <label htmlFor="base-model">Base Model</label>
                <select 
                  id="base-model"
                  value={baseModel}
                  onChange={(e) => setBaseModel(e.target.value)}
                  required
                >
                  {parameters.base_models.map(model => (
                    <option key={model.id} value={model.id}>
                      {model.name} ({model.parameters})
                    </option>
                  ))}
                </select>
                <div className="form-help">
                  The pre-trained model that will be fine-tuned with LoRA/QLoRA
                </div>
              </div>
              
              <div className="form-group">
                <label htmlFor="dataset">Dataset</label>
                <select 
                  id="dataset"
                  value={dataset}
                  onChange={(e) => setDataset(e.target.value)}
                  required
                  disabled={availableDatasets.length === 0}
                >
                  {availableDatasets.length === 0 ? (
                    <option value="">No datasets available for this domain</option>
                  ) : (
                    availableDatasets.map(ds => (
                      <option key={ds.id} value={ds.id}>
                        {ds.name} ({ds.samples.toLocaleString()} samples)
                      </option>
                    ))
                  )}
                </select>
                <div className="form-help">
                  The dataset used to fine-tune the model for your domain
                </div>
              </div>
            </div>
            
            <div className="form-section">
              <h3>LoRA Parameters</h3>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="lora-rank">LoRA Rank</label>
                  <select 
                    id="lora-rank"
                    value={loraRank}
                    onChange={(e) => setLoraRank(Number(e.target.value))}
                  >
                    {parameters.lora_parameters.lora_rank.options.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="form-help">
                    Rank of LoRA matrices (higher = more capacity)
                  </div>
                </div>
                
                <div className="form-group">
                  <label htmlFor="lora-alpha">LoRA Alpha</label>
                  <select 
                    id="lora-alpha"
                    value={loraAlpha}
                    onChange={(e) => setLoraAlpha(Number(e.target.value))}
                  >
                    {parameters.lora_parameters.lora_alpha.options.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="form-help">
                    LoRA alpha parameter (scaling factor)
                  </div>
                </div>
                
                <div className="form-group">
                  <label htmlFor="lora-dropout">LoRA Dropout</label>
                  <select 
                    id="lora-dropout"
                    value={loraDropout}
                    onChange={(e) => setLoraDropout(Number(e.target.value))}
                  >
                    {parameters.lora_parameters.lora_dropout.options.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="form-help">
                    Dropout probability for regularization
                  </div>
                </div>
              </div>
            </div>
            
            <div className="form-section">
              <h3>Training Parameters</h3>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="learning-rate">Learning Rate</label>
                  <select 
                    id="learning-rate"
                    value={learningRate}
                    onChange={(e) => setLearningRate(Number(e.target.value))}
                  >
                    {parameters.training_parameters.learning_rate.options.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="form-help">
                    Step size for gradient descent
                  </div>
                </div>
                
                <div className="form-group">
                  <label htmlFor="batch-size">Batch Size</label>
                  <select 
                    id="batch-size"
                    value={batchSize}
                    onChange={(e) => setBatchSize(Number(e.target.value))}
                  >
                    {parameters.training_parameters.batch_size.options.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="form-help">
                    Number of samples processed at once
                  </div>
                </div>
                
                <div className="form-group">
                  <label htmlFor="num-epochs">Epochs</label>
                  <select 
                    id="num-epochs"
                    value={numEpochs}
                    onChange={(e) => setNumEpochs(Number(e.target.value))}
                  >
                    {parameters.training_parameters.num_epochs.options.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="form-help">
                    Number of training passes through the dataset
                  </div>
                </div>
              </div>
            </div>
            
            <div className="form-section">
              <h3>Quantization</h3>
              <div className="form-group">
                <label htmlFor="quantization">Quantization Method</label>
                <select 
                  id="quantization"
                  value={quantization}
                  onChange={(e) => setQuantization(e.target.value)}
                >
                  {parameters.quantization.options.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                <div className="form-help">
                  Reduce model precision to save memory (4-bit recommended)
                </div>
              </div>
            </div>
            
            <div className="form-actions">
              <button 
                type="button" 
                className="secondary-button"
                onClick={handleReset}
              >
                Reset to Defaults
              </button>
              <button 
                type="submit" 
                className="primary-button"
                disabled={!apiKey || availableDatasets.length === 0}
              >
                Start Fine-Tuning
              </button>
            </div>
            
            {!apiKey && (
              <div className="api-key-warning">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                  <line x1="12" y1="9" x2="12" y2="13"></line>
                  <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
                <span>Please set your API key first by clicking the key icon in the top right</span>
              </div>
            )}
            
            {availableDatasets.length === 0 && (
              <div className="dataset-warning">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                  <line x1="12" y1="9" x2="12" y2="13"></line>
                  <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
                <span>No datasets available for the selected domain. Please select a different domain.</span>
              </div>
            )}
          </form>
        )}
        
        {activeTab === 'status' && (
          <div className="job-status-container">
            {!jobId ? (
              <div className="no-job-message">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                <h3>No Active Fine-Tuning Job</h3>
                <p>Start a new fine-tuning job to see its status here</p>
                <button 
                  className="primary-button"
                  onClick={() => setActiveTab('new')}
                >
                  Start New Job
                </button>
              </div>
            ) : (
              <div className="job-details">
                <h3>Fine-Tuning Job Status</h3>
                
                <div className="job-info">
                  <div className="job-info-item">
                    <span className="job-info-label">Job ID:</span>
                    <span className="job-info-value">{jobId}</span>
                  </div>
                  
                  <div className="job-info-item">
                    <span className="job-info-label">Status:</span>
                    <span className={`job-status-badge ${jobStatus?.status}`}>
                      {jobStatus?.status || 'Unknown'}
                    </span>
                  </div>
                  
                  {jobStatus?.progress !== undefined && (
                    <div className="job-progress">
                      <div className="progress-bar-container">
                        <div 
                          className="progress-bar" 
                          style={{ width: `${jobStatus.progress}%` }}
                        ></div>
                      </div>
                      <span className="progress-text">{jobStatus.progress}% Complete</span>
                    </div>
                  )}
                  
                  {jobStatus?.message && (
                    <div className="job-message">
                      {jobStatus.message}
                    </div>
                  )}
                  
                  {jobStatus?.status === 'completed' && (
                    <div className="job-completed">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                      </svg>
                      <p>Fine-tuning completed successfully!</p>
                      <button 
                        className="primary-button"
                        onClick={() => setActiveTab('models')}
                      >
                        View Fine-Tuned Models
                      </button>
                    </div>
                  )}
                  
                  {jobStatus?.status === 'failed' && (
                    <div className="job-failed">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                      </svg>
                      <p>Fine-tuning failed. Please try again with different parameters.</p>
                      <button 
                        className="primary-button"
                        onClick={() => setActiveTab('new')}
                      >
                        Start New Job
                      </button>
                    </div>
                  )}
                  
                  {(jobStatus?.status === 'queued' || jobStatus?.status === 'preparing' || jobStatus?.status === 'training') && (
                    <div className="job-in-progress">
                      <div className="loading-spinner"></div>
                      <p>Your fine-tuning job is in progress. This may take 30+ minutes.</p>
                      <p className="job-note">You can close this page and come back later. The job will continue running.</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'models' && (
          <div className="models-container">
            <h3>Your Fine-Tuned Models</h3>
            
            {fineTunedModels.length === 0 ? (
              <div className="no-models-message">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                <h3>No Fine-Tuned Models Yet</h3>
                <p>Start a fine-tuning job to create your first model</p>
                <button 
                  className="primary-button"
                  onClick={() => setActiveTab('new')}
                >
                  Start Fine-Tuning
                </button>
              </div>
            ) : (
              <div className="models-grid">
                {fineTunedModels.map(model => (
                  <div key={model.id} className="model-card">
                    <div className="model-header">
                      <div className="model-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                          <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                          <line x1="12" y1="22.08" x2="12" y2="12"></line>
                        </svg>
                      </div>
                      <div className="model-name">{model.name}</div>
                    </div>
                    
                    <div className="model-details">
                      <div className="model-detail">
                        <span className="detail-label">Base Model:</span>
                        <span className="detail-value">{model.base_model.split('/').pop()}</span>
                      </div>
                      
                      <div className="model-detail">
                        <span className="detail-label">Domain:</span>
                        <span className="detail-value">{model.domain}</span>
                      </div>
                      
                      <div className="model-detail">
                        <span className="detail-label">Dataset:</span>
                        <span className="detail-value">{model.dataset}</span>
                      </div>
                      
                      <div className="model-detail">
                        <span className="detail-label">Created:</span>
                        <span className="detail-value">
                          {new Date(model.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    
                    <div className="model-actions">
                      <button className="model-action-button">
                        Use Model
                      </button>
                      <button className="model-action-button secondary">
                        Details
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Fine-tuning tutorial modal */}
      <FineTuningTutorial 
        isOpen={showTutorial}
        onClose={() => setShowTutorial(false)}
      />
    </div>
  );
};

export default FineTuningTab;
