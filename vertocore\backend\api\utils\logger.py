"""
Custom logger for VertoAI backend
Provides a minimal logging interface that avoids sensitive information
"""

import logging
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# Create logger
logger = logging.getLogger('vertoai')

# Set log level based on environment
if os.environ.get('DEBUG', 'False').lower() == 'true':
    logger.setLevel(logging.DEBUG)
else:
    logger.setLevel(logging.INFO)

# Custom log functions that avoid sensitive information
def info(message):
    """Log info message without sensitive information"""
    logger.info(message)

def debug(message):
    """Log debug message without sensitive information"""
    if os.environ.get('DEBUG', 'False').lower() == 'true':
        logger.debug(message)

def warning(message):
    """Log warning message without sensitive information"""
    logger.warning(message)

def error(message):
    """Log error message without sensitive information"""
    logger.error(message)

def api_request(method, endpoint):
    """Log API request without sensitive information"""
    debug(f"API Request: {method} {endpoint}")

def api_response(status_code, endpoint):
    """Log API response without sensitive information"""
    debug(f"API Response: {status_code} from {endpoint}")
