import React, { useEffect, useRef } from 'react';
import * as BABYLON from 'babylonjs';
import 'babylonjs-loaders';
import 'babylonjs-materials';
import 'babylonjs-procedural-textures';

const BabylonBackground = ({ theme }) => {
  const canvasRef = useRef(null);
  const engineRef = useRef(null);
  const sceneRef = useRef(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Initialize Babylon.js engine
    const engine = new BABYLON.Engine(canvasRef.current, true, { preserveDrawingBuffer: true, stencil: true });
    engineRef.current = engine;

    // Create scene
    const createScene = () => {
      const scene = new BABYLON.Scene(engine);
      scene.clearColor = new BABYLON.Color4(0, 0, 0, 0); // Transparent background

      // Add camera
      const camera = new BABYLON.ArcRotateCamera("Camera", -Math.PI / 2, Math.PI / 2, 10, BABYLON.Vector3.Zero(), scene);
      camera.attachControl(canvasRef.current, true);
      camera.alpha = Math.PI / 1.2;
      camera.beta = Math.PI / 2.5;
      camera.radius = 30;
      camera.minZ = 0.1;
      camera.fov = 0.8; // Wider field of view

      // Disable camera controls
      camera.inputs.clear();

      // Add lights
      const hemisphericLight = new BABYLON.HemisphericLight("light", new BABYLON.Vector3(0, 1, 0), scene);
      hemisphericLight.intensity = 0.7;

      const pointLight = new BABYLON.PointLight("pointLight", new BABYLON.Vector3(0, 5, 0), scene);
      pointLight.intensity = 0.5;

      // Add volumetric light
      const spotLight = new BABYLON.SpotLight("spotLight", new BABYLON.Vector3(0, 10, 0), new BABYLON.Vector3(0, -1, 0), Math.PI / 3, 2, scene);
      spotLight.intensity = 0.7;

      // Set theme-specific colors
      let particleColor, glowColor;

      switch (theme) {
        case 'light':
          particleColor = new BABYLON.Color3(0.29, 0.42, 0.97); // #4a6cf7
          glowColor = new BABYLON.Color3(0.43, 0.28, 0.9); // #6e48e6
          break;
        case 'cosmic':
          particleColor = new BABYLON.Color3(0.45, 0.01, 0.75); // #7303c0
          glowColor = new BABYLON.Color3(0.93, 0.22, 0.74); // #ec38bc
          break;
        case 'aurora':
          particleColor = new BABYLON.Color3(0.33, 0.56, 0.85); // #5390d9
          glowColor = new BABYLON.Color3(0.31, 0.66, 0.87); // #4ea8de
          break;
        case 'nebula':
          particleColor = new BABYLON.Color3(0.58, 0.2, 0.92); // #9333ea
          glowColor = new BABYLON.Color3(0.66, 0.33, 0.97); // #a855f7
          break;
        case 'quantum':
          particleColor = new BABYLON.Color3(0.02, 0.59, 0.41); // #059669
          glowColor = new BABYLON.Color3(0.06, 0.73, 0.51); // #10b981
          break;
        case 'synthwave':
          particleColor = new BABYLON.Color3(1, 0, 0.5); // #ff0080
          glowColor = new BABYLON.Color3(1, 0.55, 0); // #ff8c00
          break;
        default:
          particleColor = new BABYLON.Color3(0.29, 0.42, 0.97); // #4a6cf7
          glowColor = new BABYLON.Color3(0.43, 0.28, 0.9); // #6e48e6
      }

      // Create a more advanced particle system for the background
      const particleSystem = new BABYLON.GPUParticleSystem("particles", { capacity: 5000 }, scene);

      // Particle texture
      particleSystem.particleTexture = new BABYLON.Texture("data:image/png;base64,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", scene);

      // Configure particle system
      particleSystem.emitter = new BABYLON.Vector3(0, 0, 0);
      particleSystem.minEmitBox = new BABYLON.Vector3(-25, -25, -25);
      particleSystem.maxEmitBox = new BABYLON.Vector3(25, 25, 25);

      particleSystem.color1 = particleColor;
      particleSystem.color2 = glowColor;
      particleSystem.colorDead = new BABYLON.Color4(0, 0, 0, 0);

      particleSystem.minSize = 0.1;
      particleSystem.maxSize = 0.5;

      particleSystem.minLifeTime = 5;
      particleSystem.maxLifeTime = 15;

      particleSystem.emitRate = 300;

      particleSystem.blendMode = BABYLON.ParticleSystem.BLENDMODE_ADD;

      particleSystem.gravity = new BABYLON.Vector3(0, 0, 0);

      particleSystem.direction1 = new BABYLON.Vector3(-1, -1, -1);
      particleSystem.direction2 = new BABYLON.Vector3(1, 1, 1);

      particleSystem.minAngularSpeed = 0;
      particleSystem.maxAngularSpeed = Math.PI;

      particleSystem.minEmitPower = 0.5;
      particleSystem.maxEmitPower = 2.0;
      particleSystem.updateSpeed = 0.01;

      // Add particle trails
      particleSystem.targetStopDuration = 0.2;
      particleSystem.textureMask = new BABYLON.Color4(1, 1, 1, 1);

      // Add noise to make particles move more naturally
      const noiseTexture = new BABYLON.NoiseProceduralTexture("perlin", 256, scene);
      noiseTexture.animationSpeedFactor = 5;
      noiseTexture.persistence = 0.65;
      noiseTexture.brightness = 0.9;
      noiseTexture.octaves = 8;

      particleSystem.noiseTexture = noiseTexture;
      particleSystem.noiseStrength = new BABYLON.Vector3(10, 10, 10);

      // Create advanced floating 3D objects
      const createFloatingObject = (position, scale, rotation) => {
        // Create a PBR (Physically Based Rendering) material for more realistic look
        const material = new BABYLON.PBRMaterial("pbrMaterial", scene);
        material.emissiveColor = glowColor;
        material.albedoColor = new BABYLON.Color3(0.1, 0.1, 0.1);
        material.metallic = 0.8;
        material.roughness = 0.2;
        material.alpha = 0.7;
        material.subSurface.isRefractionEnabled = true;
        material.subSurface.refractionIntensity = 0.5;

        // Create different geometric shapes
        let mesh;
        const shapeType = Math.floor(Math.random() * 6);

        switch (shapeType) {
          case 0:
            mesh = BABYLON.MeshBuilder.CreateBox("box", { size: 1, updatable: true }, scene);
            break;
          case 1:
            mesh = BABYLON.MeshBuilder.CreateSphere("sphere", { diameter: 1, segments: 32, updatable: true }, scene);
            break;
          case 2:
            mesh = BABYLON.MeshBuilder.CreateTorus("torus", { diameter: 1, thickness: 0.3, tessellation: 32, updatable: true }, scene);
            break;
          case 3:
            mesh = BABYLON.MeshBuilder.CreatePolyhedron("polyhedron", { type: Math.floor(Math.random() * 5), size: 0.5, updatable: true }, scene);
            break;
          case 4:
            mesh = BABYLON.MeshBuilder.CreateTorusKnot("torusKnot", { radius: 0.5, tube: 0.2, radialSegments: 32, tubularSegments: 32, p: 2, q: 3, updatable: true }, scene);
            break;
          case 5:
            mesh = BABYLON.MeshBuilder.CreateOctahedron("octahedron", { size: 0.7, updatable: true }, scene);
            break;
        }

        mesh.position = position;
        mesh.scaling = scale;
        mesh.rotation = rotation;
        mesh.material = material;

        // Add glow layer effect
        const glowLayer = new BABYLON.GlowLayer("glow", scene);
        glowLayer.intensity = 0.7;

        // Create a highlight layer for additional effect
        const highlightLayer = new BABYLON.HighlightLayer("highlightLayer", scene);
        highlightLayer.addMesh(mesh, glowColor);

        // Add environment texture for reflections
        const envTexture = new BABYLON.CubeTexture("", scene);
        envTexture.reflectionTexture = new BABYLON.CubeTexture("", scene);
        material.reflectionTexture = envTexture;

        // Advanced animation
        const animationSpeed = Math.random() * 0.01 + 0.005;
        scene.registerBeforeRender(() => {
          mesh.rotation.x += animationSpeed;
          mesh.rotation.y += animationSpeed * 0.7;
          mesh.rotation.z += animationSpeed * 0.5;

          // Subtle position animation with noise
          const time = engine.getDeltaTime() * 0.001;
          mesh.position.x = position.x + Math.sin(time) * 0.5 + Math.sin(time * 2.5) * 0.2;
          mesh.position.y = position.y + Math.cos(time * 1.5) * 0.5 + Math.cos(time * 3.5) * 0.2;
          mesh.position.z = position.z + Math.sin(time * 2) * 0.5 + Math.sin(time * 4.5) * 0.2;

          // Subtle scale animation
          const scaleFactor = 1 + Math.sin(time * 3) * 0.1;
          mesh.scaling.x = scale.x * scaleFactor;
          mesh.scaling.y = scale.y * scaleFactor;
          mesh.scaling.z = scale.z * scaleFactor;

          // Subtle material animation
          material.emissiveIntensity = 0.7 + Math.sin(time * 2) * 0.3;
        });

        return mesh;
      };

      // Create several floating objects
      for (let i = 0; i < 10; i++) {
        const position = new BABYLON.Vector3(
          (Math.random() - 0.5) * 20,
          (Math.random() - 0.5) * 20,
          (Math.random() - 0.5) * 20
        );

        const scale = new BABYLON.Vector3(
          Math.random() * 0.5 + 0.5,
          Math.random() * 0.5 + 0.5,
          Math.random() * 0.5 + 0.5
        );

        const rotation = new BABYLON.Vector3(
          Math.random() * Math.PI * 2,
          Math.random() * Math.PI * 2,
          Math.random() * Math.PI * 2
        );

        createFloatingObject(position, scale, rotation);
      }

      // Create a more advanced 3D grid system
      const createHolographicGrid = () => {
        const gridSize = 40;
        const gridDivisions = 20;

        // Create grid container
        const gridContainer = new BABYLON.TransformNode("gridContainer", scene);

        // Create multiple grid planes for a more 3D effect
        const createGridPlane = (height, alpha, divisions) => {
          const gridMaterial = new BABYLON.StandardMaterial("gridMaterial", scene);
          gridMaterial.emissiveColor = particleColor;
          gridMaterial.alpha = alpha;
          gridMaterial.wireframe = true;

          // Create a grid using lines for better performance
          const gridLines = new BABYLON.LinesMesh("gridLines", scene);
          const positions = [];
          const indices = [];
          let idx = 0;

          // Create horizontal lines
          for (let i = 0; i <= divisions; i++) {
            const pos = -gridSize/2 + (gridSize * i / divisions);
            positions.push(-gridSize/2, pos, height);
            positions.push(gridSize/2, pos, height);
            indices.push(idx, idx + 1);
            idx += 2;
          }

          // Create vertical lines
          for (let i = 0; i <= divisions; i++) {
            const pos = -gridSize/2 + (gridSize * i / divisions);
            positions.push(pos, -gridSize/2, height);
            positions.push(pos, gridSize/2, height);
            indices.push(idx, idx + 1);
            idx += 2;
          }

          const vertexData = new BABYLON.VertexData();
          vertexData.positions = positions;
          vertexData.indices = indices;
          vertexData.applyToMesh(gridLines);

          gridLines.color = particleColor;
          gridLines.alpha = alpha;
          gridLines.parent = gridContainer;

          return gridLines;
        };

        // Create multiple grid planes at different heights
        const mainGrid = createGridPlane(0, 0.4, gridDivisions);
        const topGrid = createGridPlane(10, 0.2, gridDivisions / 2);
        const bottomGrid = createGridPlane(-10, 0.2, gridDivisions / 2);

        // Create vertical connecting lines for a 3D effect
        const createVerticalConnectors = () => {
          const connectorMaterial = new BABYLON.StandardMaterial("connectorMaterial", scene);
          connectorMaterial.emissiveColor = glowColor;
          connectorMaterial.alpha = 0.15;

          for (let i = 0; i < 8; i++) {
            const x = (Math.random() - 0.5) * gridSize;
            const y = (Math.random() - 0.5) * gridSize;

            const connector = BABYLON.MeshBuilder.CreateLines(`connector${i}`, {
              points: [
                new BABYLON.Vector3(x, y, -10),
                new BABYLON.Vector3(x, y, 10)
              ]
            }, scene);

            connector.color = glowColor;
            connector.alpha = 0.3;
            connector.parent = gridContainer;
          }
        };

        createVerticalConnectors();

        // Create pulsing effect for the grid
        let time = 0;
        scene.registerBeforeRender(() => {
          time += engine.getDeltaTime() * 0.001;

          // Rotate the entire grid container
          gridContainer.rotation.x = Math.sin(time * 0.1) * 0.1;
          gridContainer.rotation.y += 0.0005;
          gridContainer.rotation.z = Math.cos(time * 0.15) * 0.05;

          // Pulse effect
          const pulse = (Math.sin(time) + 1) * 0.5 * 0.3 + 0.2;
          mainGrid.alpha = pulse + 0.1;
          topGrid.alpha = pulse * 0.5;
          bottomGrid.alpha = pulse * 0.5;
        });

        return gridContainer;
      };

      // Create the holographic grid
      const holographicGrid = createHolographicGrid();

      // Add floating data streams for a more futuristic look
      const createDataStreams = () => {
        const streamCount = 15;
        const streamContainer = new BABYLON.TransformNode("streamContainer", scene);

        for (let i = 0; i < streamCount; i++) {
          const height = (Math.random() - 0.5) * 30;
          const radius = 15 + Math.random() * 10;
          const speed = 0.5 + Math.random() * 1.5;
          const size = 0.05 + Math.random() * 0.15;

          const path = [];
          const segments = 10 + Math.floor(Math.random() * 10);

          for (let j = 0; j <= segments; j++) {
            const angle = (j / segments) * Math.PI * 2;
            path.push(new BABYLON.Vector3(
              Math.cos(angle) * radius,
              Math.sin(angle) * radius,
              height + (Math.random() - 0.5) * 2
            ));
          }

          const tube = BABYLON.MeshBuilder.CreateTube(`dataStream${i}`, {
            path: path,
            radius: size,
            tessellation: 8,
            updatable: true
          }, scene);

          const streamMaterial = new BABYLON.StandardMaterial(`streamMaterial${i}`, scene);
          streamMaterial.emissiveColor = i % 2 === 0 ? particleColor : glowColor;
          streamMaterial.alpha = 0.6;
          tube.material = streamMaterial;
          tube.parent = streamContainer;

          // Animate the data stream
          let offset = Math.random() * Math.PI * 2;
          scene.registerBeforeRender(() => {
            offset += engine.getDeltaTime() * 0.001 * speed;
            streamMaterial.alpha = 0.3 + Math.sin(offset) * 0.3;
            tube.rotation.y = offset * 0.2;
          });
        }

        return streamContainer;
      };

      // Create data streams
      const dataStreams = createDataStreams();

      // Start particle system
      particleSystem.start();

      return scene;
    };

    // Create and run the scene
    sceneRef.current = createScene();

    // Run the render loop
    engine.runRenderLoop(() => {
      if (sceneRef.current) {
        sceneRef.current.render();
      }
    });

    // Handle window resize
    const handleResize = () => {
      if (engineRef.current) {
        engineRef.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);

      if (sceneRef.current) {
        sceneRef.current.dispose();
      }

      if (engineRef.current) {
        engineRef.current.dispose();
      }
    };
  }, [theme]);

  return (
    <canvas
      ref={canvasRef}
      className="babylon-canvas"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        zIndex: -1,
        opacity: 0.9,
        pointerEvents: 'none',
        overflow: 'hidden'
      }}
    />
  );
};

export default BabylonBackground;
