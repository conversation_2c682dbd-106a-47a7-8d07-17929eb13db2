/**
 * Configuration utility for VertoAI
 * Centralizes access to environment variables and configuration
 */

// API configuration
export const API_URL = window.REACT_APP_API_URL || 'http://localhost:5000';

// Default model configuration
export const DEFAULT_MODEL = 'google/gemma-3-12b-it:free';

// Free usage limits
export const FREE_USAGE_LIMIT = 10;

// Testing mode - set to true to disable usage limits
export const TESTING_MODE = true;

// Separate API key for title generation (to avoid using main quota)
export const USE_SEPARATE_TITLE_KEY = true;

// Default domains
export const DOMAINS = [
  {
    id: 'fintech',
    name: 'Finance & Banking',
    description: 'Financial services, banking, investments, and regulations'
  },
  {
    id: 'healthcare',
    name: 'Healthcare',
    description: 'Medical information, healthcare systems, and patient care'
  },
  {
    id: 'education',
    name: 'Education',
    description: 'Learning, teaching, educational systems, and research'
  },
  {
    id: 'law',
    name: 'Legal',
    description: 'Legal information, regulations, and compliance'
  }
];

// Default providers
export const PROVIDERS = [
  {
    id: 'openrouter',
    name: 'OpenRouter',
    models: ['google/gemma-3-12b-it:free'],
    requiresApiKey: false,  // False because we have a shared API key for free tier
    defaultForFree: true,
    description: 'Free tier: 10 prompts/day shared limit. Add your own API key for 50+ prompts/day.'
  },
  {
    id: 'openai',
    name: 'OpenAI',
    models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
    requiresApiKey: true
  },
  {
    id: 'gemini',
    name: 'Google Gemini',
    models: ['gemini-pro', 'gemini-ultra'],
    requiresApiKey: true
  },
  {
    id: 'claude',
    name: 'Anthropic Claude',
    models: ['claude-instant-1', 'claude-2'],
    requiresApiKey: true
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    models: ['deepseek-coder', 'deepseek-llm'],
    requiresApiKey: true
  }
];
