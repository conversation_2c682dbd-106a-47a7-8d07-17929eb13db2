/**
 * Theme toggler utility for VertoAI
 * Handles switching between light and dark themes
 */

// Theme options
const THEMES = {
  LIGHT: 'light',
  DARK: 'dark'
};

// Check if user has a saved theme preference
const getSavedTheme = () => {
  const savedTheme = localStorage.getItem('verto-theme');
  return savedTheme || THEMES.LIGHT;
};

// Check if user has system preference for dark mode
const getSystemTheme = () => {
  return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
    ? THEMES.DARK
    : THEMES.LIGHT;
};

// Initialize theme based on saved preference or system preference
const initializeTheme = () => {
  const savedTheme = getSavedTheme();
  const systemTheme = getSystemTheme();
  const theme = savedTheme || systemTheme;
  
  applyTheme(theme);
  return theme;
};

// Apply theme to document
const applyTheme = (theme) => {
  document.documentElement.setAttribute('data-theme', theme);
  localStorage.setItem('verto-theme', theme);
  
  // Apply theme class to body
  if (theme === THEMES.DARK) {
    document.body.classList.add('dark-theme');
    document.body.classList.remove('light-theme');
  } else {
    document.body.classList.add('light-theme');
    document.body.classList.remove('dark-theme');
  }
};

// Toggle between light and dark themes
const toggleTheme = () => {
  const currentTheme = getSavedTheme();
  const newTheme = currentTheme === THEMES.LIGHT ? THEMES.DARK : THEMES.LIGHT;
  
  applyTheme(newTheme);
  return newTheme;
};

export {
  THEMES,
  initializeTheme,
  toggleTheme,
  getSavedTheme,
  applyTheme
};
