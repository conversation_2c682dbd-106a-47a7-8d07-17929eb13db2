# VertoAI Environment Variables

# Flask configuration
DEBUG=True
SECRET_KEY=your-secret-key-change-in-production
PORT=5000

# CORS configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# API keys (replace with your actual keys)
OPENAI_API_KEY=
OPENROUTER_API_KEY=your-openrouter-api-key-here
ANTHROPIC_API_KEY=
GOOGLE_API_KEY=
DEEPSEEK_API_KEY=

# Rate limiting
RATE_LIMIT_REQUESTS=500
RATE_LIMIT_PERIOD=86400  # 24 hours in seconds

# Fine-tuning configuration
FINE_TUNING_ENABLED=True
MAX_CONCURRENT_JOBS=2
MODEL_STORAGE_PATH=./models

# LangChain configuration
LANGCHAIN_TRACING=False
LANGCHAIN_PROJECT=vertoai
