import React from 'react';
import Dropdown from './Dropdown';

const DomainSelector = ({ selectedDomain, onDomainChange }) => {
  const domains = [
    { id: 'fintech', label: 'Fintech', color: '#4caf50' },
    { id: 'healthcare', label: 'Healthcare', color: '#2196f3' },
    { id: 'education', label: 'Education', color: '#ff9800' },
    { id: 'law', label: 'Law', color: '#9c27b0' }
  ];

  return (
    <div className="control-section">
      <h2>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
          <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
        </svg>
        Select Domain
      </h2>
      <Dropdown
        options={domains}
        selectedValue={selectedDomain}
        onSelect={onDomainChange}
        placeholder="Select a domain"
      />
    </div>
  );
};

export default DomainSelector;
