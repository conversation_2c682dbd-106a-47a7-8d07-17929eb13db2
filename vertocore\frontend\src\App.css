@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Base theme colors */
  --primary-color: #4a6cf7;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --info-color: #17a2b8;
  --warning-color: #ffc107;
  --danger-color: #dc3545;

  /* RGB versions of gradient colors for opacity control */
  --gradient-start-rgb: 74, 108, 247;
  --gradient-mid-rgb: 110, 72, 230;
  --gradient-end-rgb: 147, 51, 234;

  /* Dark theme colors */
  --dark-bg: #121212;
  --dark-surface: #1e1e1e;
  --dark-surface-2: #2d2d2d;
  --dark-text: #e0e0e0;
  --dark-text-secondary: #a0a0a0;
  --dark-border: #333333;

  /* Light theme colors */
  --light-bg: #f5f8ff;
  --light-surface: #ffffff;
  --light-surface-2: #f0f0f0;
  --light-text: #333333;
  --light-text-secondary: #666666;
  --light-border: #e1e4e8;

  /* Current theme colors (default to dark) */
  --bg-color: var(--dark-bg);
  --surface-color: var(--dark-surface);
  --surface-color-2: var(--dark-surface-2);
  --text-color: var(--dark-text);
  --text-color-secondary: var(--dark-text-secondary);
  --border-color: var(--dark-border);

  /* Gradient colors */
  --gradient-start: #4a6cf7;
  --gradient-mid: #6e48e6;
  --gradient-end: #9333ea;
}

body {
  font-family: 'Inter', 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Custom focus styles for better accessibility */
:focus {
  outline: none;
}

/* Custom focus style for interactive elements */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[role="button"]:focus-visible,
[tabindex]:focus-visible {
  box-shadow: 0 0 0 2px rgba(var(--gradient-start-rgb), 0.5);
  border-radius: 4px;
  outline: none;
}

#root {
  width: 100%;
  min-height: 100vh;
  position: relative;
}

/* Three.js background */
.background-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 0.8;
}

.app-container {
  max-width: 2000px; /* Further increased for larger chat container */
  margin: 0 auto;
  padding: 2rem 2rem 0 2rem; /* Removed bottom padding */
  display: flex;
  flex-direction: column;
  height: auto; /* Changed from min-height: 100vh to prevent extra space */
  background-color: rgba(18, 18, 18, 0.7); /* More opaque for better readability */
  border-radius: 12px;
  position: relative;
  z-index: 1;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  overflow: hidden; /* Prevent content from overflowing */
}

/* History Panel */
.app-layout {
  display: flex;
  gap: 1rem; /* Reduced gap between panels */
  flex-grow: 1;
  align-items: flex-start; /* Align items at the top */
  min-height: calc(100vh - 140px); /* Further increased height to match history panel */
  padding: 0 1rem; /* Increased horizontal padding */
  width: 100%; /* Ensure full width */
  max-width: 1800px; /* Increased maximum width to match chat container */
  margin: 0 auto; /* Center the layout */
  overflow: hidden; /* Prevent overflow when resizing */
}

.history-panel {
  width: 320px; /* Default width */
  min-width: 250px; /* Minimum width allowed */
  max-width: 500px; /* Maximum width allowed */
  flex-shrink: 0; /* Prevent shrinking */
  background-color: rgba(30, 30, 45, 0.9); /* Darker, semi-transparent background */
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 20px rgba(var(--gradient-start-rgb), 0.25); /* Enhanced glow effect */
  border: 1px solid rgba(var(--gradient-start-rgb), 0.4); /* Glowing border using primary color */
  height: calc(100vh - 140px); /* Further increased height to match chat container */
  max-height: 950px; /* Increased maximum height */
  position: relative; /* Keep position relative */
  z-index: 10; /* Ensure this is lower than modal z-index but above other content */
  backdrop-filter: blur(8px); /* Adds a modern glass effect */
  align-self: flex-start; /* Ensure it aligns at the top */
  margin-bottom: 20px; /* Add some bottom margin */
  display: grid;
  grid-template-rows: auto auto auto 1fr; /* Header, Search, Tabs, Content */
  overflow-x: hidden; /* Hide horizontal scrollbar */
  transition: width 0.05s ease; /* Smooth width transition */
}

/* Custom resize handle */
.resize-handle {
  position: absolute;
  top: 0;
  right: -5px;
  width: 10px;
  height: 100%;
  cursor: ew-resize;
  z-index: 20;
  display: flex;
  justify-content: center;
  align-items: center;
}

.resize-handle-line {
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom,
    transparent,
    rgba(var(--gradient-start-rgb), 0.3),
    rgba(var(--gradient-mid-rgb), 0.4),
    rgba(var(--gradient-end-rgb), 0.3),
    transparent
  );
  opacity: 0;
  transition: opacity 0.2s ease;
}

.resize-handle:hover .resize-handle-line,
.history-panel.resizing .resize-handle-line {
  opacity: 1;
}

/* Add a visual indicator when resizing */
.history-panel.resizing {
  user-select: none;
}

/* Reset chat content margin */
.chat-content {
  margin-left: 0;
}

.history-header {
  padding: 1rem 1.25rem; /* Reduced padding */
  border-bottom: 1px solid rgba(var(--gradient-start-rgb), 0.4);
  background-color: var(--surface-color-2); /* Match chat header background */
  display: flex;
  justify-content: flex-start; /* Changed from space-between to flex-start */
  align-items: center;
  z-index: 30; /* Increased z-index to ensure visibility */
  overflow: visible; /* Changed from hidden to visible */
  height: 60px; /* Reduced height */
  grid-row: 1; /* Place in the first row of the grid */
  border-top-left-radius: 16px; /* Match panel border radius */
  border-top-right-radius: 16px; /* Match panel border radius */
  box-sizing: border-box; /* Ensure padding is included in height */
  position: relative; /* Ensure proper positioning context */
}

.history-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent,
    rgba(var(--gradient-start-rgb), 0.8),
    rgba(var(--gradient-end-rgb), 0.8),
    transparent
  );
  z-index: 1;
}

.history-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  font-family: 'Orbitron', sans-serif; /* Futuristic font */
  letter-spacing: 1px;
  text-transform: uppercase;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 5px rgba(var(--gradient-start-rgb), 0.5);
  line-height: 1; /* Reduced line height */
  white-space: nowrap; /* Prevent wrapping */
  display: flex;
  align-items: center;
  height: 100%;
  margin-right: 1rem; /* Add right margin to create space */
}

.history-buttons {
  display: flex;
  gap: 0.75rem;
  position: relative;
  z-index: 20; /* Ensure buttons are clickable */
  align-items: center; /* Ensure vertical alignment */
  height: 100%; /* Take full height of parent */
  margin-left: auto; /* Push to the right */
}

.new-chat-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.35rem 0.6rem;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(var(--gradient-start-rgb), 0.4);
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 100;
  height: 26px; /* Reduced height */
  line-height: 1; /* Ensure text is vertically centered */
  white-space: nowrap; /* Prevent text wrapping */
  margin-right: 0.5rem; /* Add margin to the right */
  margin-left: 0; /* Reset left margin */
  transform: none; /* Reset transform */
  top: 0; /* Reset top positioning */
  box-sizing: border-box; /* Include padding in height calculation */
}

.delete-all-chats-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-color-2);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.4rem;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 26px; /* Match new-chat-button height */
  width: 26px; /* Make it square */
  box-sizing: border-box; /* Include padding in height calculation */
}

.delete-all-chats-button:hover {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  border-color: #dc3545;
}

/* Removed sliding white shade effect */

.new-chat-button:hover {
  box-shadow: 0 4px 12px rgba(var(--gradient-start-rgb), 0.5);
  background: linear-gradient(135deg, var(--gradient-end), var(--gradient-start));
  opacity: 0.9;
}

.new-chat-button:active {
  box-shadow: 0 2px 8px rgba(var(--gradient-start-rgb), 0.3);
  opacity: 1;
}

.new-chat-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--gradient-start-rgb), 0.5);
}

.history-search {
  padding: 0.85rem 1rem;
  border-bottom: 1px solid var(--border-color);
  z-index: 29; /* Just below header z-index */
  background-color: var(--surface-color); /* Match chat panel background */
  height: 60px; /* Keep increased height */
  grid-row: 2; /* Place in the second row of the grid */
  display: flex;
  align-items: center;
}

.history-search-input {
  width: calc(100% - 2rem); /* Adjust width to add margin */
  padding: 0.7rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(var(--gradient-start-rgb), 0.2);
  background-color: rgba(30, 30, 45, 0.6);
  color: var(--text-color);
  font-size: 0.95rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Inter', sans-serif;
  height: 40px; /* Increased height */
  margin: 0 1rem; /* Add horizontal margin */
}

.history-search-input:focus {
  outline: none;
  border-color: rgba(var(--gradient-start-rgb), 0.5);
  box-shadow: 0 0 0 2px rgba(var(--gradient-start-rgb), 0.3);
}

.clear-search-button {
  position: absolute;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-color-secondary);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--surface-color-2); /* Match chat panel style */
  z-index: 28; /* Just below search bar z-index */
  overflow: visible; /* Changed from hidden to visible */
  height: 40px; /* Reduced height */
  grid-row: 3; /* Place in the third row of the grid */
  padding: 0 0.5rem; /* Reduced left padding */
  box-sizing: border-box; /* Ensure padding is included in height */
  margin: 0; /* No margin */
  width: 100%; /* Full width */
}

.history-tabs::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent,
    rgba(var(--gradient-start-rgb), 0.6),
    rgba(var(--gradient-end-rgb), 0.6),
    transparent
  );
  z-index: 1;
}

.history-tab {
  flex: 0 1 auto; /* Don't grow, can shrink, auto basis */
  padding: 0.5rem 1rem; /* Increased horizontal padding */
  text-align: center;
  background: none;
  border: none;
  color: var(--text-color-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 2px solid transparent;
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 0.75rem;
  position: relative;
  overflow: hidden;
  z-index: 1;
  height: 38px; /* Slightly less than container height */
  line-height: 1;
  margin-right: 0.75rem; /* Only right margin */
  margin-left: 0.5rem; /* Small left margin */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px; /* Fixed width */
  min-width: 80px; /* Ensure minimum width */
}

/* First tab should be at extreme left */
.history-tab:first-child {
  margin-left: 0;
  padding-left: 0.5rem;
}

.history-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(var(--gradient-start-rgb), 0.1),
    rgba(var(--gradient-end-rgb), 0.1)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.history-tab:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--gradient-start-rgb), 0.3);
}

.history-tab:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
  transition: width 0.3s ease;
}

.history-tab:hover::before {
  opacity: 0.5;
}

.history-tab.active {
  color: var(--gradient-start);
  font-weight: 700;
  background: linear-gradient(135deg,
    rgba(var(--gradient-start-rgb), 0.15),
    rgba(var(--gradient-end-rgb), 0.15)
  );
  box-shadow: 0 0 10px rgba(var(--gradient-start-rgb), 0.2);
  font-size: 0.75rem;
}

.history-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
  box-shadow: 0 0 5px rgba(var(--gradient-start-rgb), 0.8);
}

.history-list {
  overflow-y: auto;
  padding: 1.5rem; /* Match chat messages padding */
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--gradient-start-rgb), 0.3) rgba(30, 30, 45, 0.2);
  background-color: var(--surface-color); /* Match chat messages background */
  display: flex;
  flex-direction: column;
  gap: 1.25rem; /* Match chat messages gap */
  position: relative;
  z-index: 25; /* Lower than tabs but still visible */
  grid-row: 4; /* Place in the fourth row of the grid */
  margin: 0; /* Reset margin */
  border-bottom-left-radius: 16px; /* Match panel border radius */
  border-bottom-right-radius: 16px; /* Match panel border radius */
}

.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: rgba(30, 30, 45, 0.2);
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
  background: rgba(var(--gradient-start-rgb), 0.3);
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--gradient-start-rgb), 0.5);
}

.history-item {
  padding: 0.5rem 0.75rem; /* Further reduced padding */
  border-radius: 8px; /* Smaller radius */
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid var(--border-color);
  background-color: var(--surface-color-2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 0 0.5rem 0; /* Add bottom margin, reset others */
  z-index: 20; /* Ensure items are visible */
  width: calc(100% - 1rem); /* Even wider than before */
  margin-left: 0.25rem; /* Further shifted left */
  margin-right: auto;
  max-height: 65px; /* Reduced maximum height */
}

.history-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(var(--gradient-start-rgb), 0.05),
    rgba(var(--gradient-end-rgb), 0.05)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
  z-index: -1;
}

.history-item:hover {
  background-color: rgba(var(--gradient-start-rgb), 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(var(--gradient-start-rgb), 0.3);
}

.history-item.active {
  background-color: rgba(var(--gradient-start-rgb), 0.1);
  border: 1px solid rgba(var(--gradient-start-rgb), 0.4);
  box-shadow: 0 0 10px rgba(var(--gradient-start-rgb), 0.2);
  transform: translateY(-2px);
  position: relative;
}

.history-item.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--gradient-start), var(--gradient-end));
  border-radius: 4px 0 0 4px;
}

@keyframes pulse-glow {
  0% {
    opacity: 0.3;
    box-shadow: 0 0 5px rgba(var(--gradient-start-rgb), 0.3);
  }
  50% {
    opacity: 0.6;
    box-shadow: 0 0 15px rgba(var(--gradient-start-rgb), 0.5);
  }
  100% {
    opacity: 0.3;
    box-shadow: 0 0 5px rgba(var(--gradient-start-rgb), 0.3);
  }
}

.history-item.pinned {
  border-left: 4px solid var(--gradient-start);
  padding-left: 1.2rem;
  position: relative;
}

.history-item.pinned::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--gradient-start), var(--gradient-end));
  box-shadow: 0 0 8px rgba(var(--gradient-start-rgb), 0.8);
  border-radius: 4px 0 0 4px;
}

.history-item.has-unread {
  font-weight: bold;
  background: linear-gradient(135deg,
    rgba(40, 167, 69, 0.15),
    rgba(40, 167, 69, 0.05)
  );
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.history-item-content {
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 0.25rem; /* Further reduced gap */
  padding-right: 0.5rem; /* Add space for action buttons */
}

.history-item-title {
  font-weight: 600;
  margin-bottom: 0; /* Removed margin */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  gap: 0.3rem; /* Reduced gap */
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.3px;
  font-size: 0.85rem; /* Smaller font size */
  color: var(--text-color);
}

.pin-indicator {
  color: var(--gradient-start);
  display: flex;
  align-items: center;
  filter: drop-shadow(0 0 2px rgba(var(--gradient-start-rgb), 0.5));
}

.unread-indicator {
  color: #28a745;
  display: flex;
  align-items: center;
  animation: pulse 1.5s infinite;
  margin-right: 4px;
  filter: drop-shadow(0 0 3px rgba(40, 167, 69, 0.5));
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

.history-item-preview {
  font-size: 0.7rem; /* Even smaller font size */
  color: var(--text-color-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 0; /* Removed margin */
  opacity: 0.8;
  max-width: 220px; /* Reduced width */
  line-height: 1.1; /* Even tighter line height */
}

.history-item-date {
  font-size: 0.6rem; /* Even smaller font size */
  color: var(--text-color-secondary);
  font-family: 'Inter', sans-serif;
  opacity: 0.7;
  font-style: italic;
  margin-top: 0; /* Removed negative margin */
}

.history-item-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: 0.5rem; /* Add space between content and buttons */
}

.history-action-button {
  width: 30px;
  height: 30px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  color: var(--text-color-secondary);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0;
  opacity: 0.8;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.history-action-button:hover {
  background-color: rgba(var(--gradient-start-rgb), 0.15);
  color: var(--gradient-start);
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: rgba(var(--gradient-start-rgb), 0.4);
}

.history-action-button:focus {
  box-shadow: 0 0 0 2px rgba(var(--gradient-start-rgb), 0.5);
  border-color: rgba(var(--gradient-start-rgb), 0.6);
  outline: none;
}

.history-action-button svg {
  stroke: currentColor;
  fill: none;
}

.history-action-button.delete:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border-color: #dc3545;
}

.empty-history-message {
  padding: 2.5rem 1rem;
  text-align: center;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.5px;
  background-color: var(--surface-color-2);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  margin: 1rem auto;
  width: calc(100% - 1rem);
  align-self: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Header styles */
.app-header {
  text-align: center;
  margin-bottom: 2.5rem;
  padding: 2rem 0 1.5rem;
  border-bottom: 1px solid rgba(var(--gradient-start-rgb), 0.3);
  position: relative;
  overflow: hidden;
}

.app-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent,
    var(--gradient-start),
    var(--gradient-mid),
    var(--gradient-end),
    transparent
  );
  animation: slide-gradient 8s infinite linear;
  z-index: 1;
}

@keyframes slide-gradient {
  0% {
    background-position: -500px 0;
  }
  100% {
    background-position: 500px 0;
  }
}

.app-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 4.5rem;
  font-weight: 800;
  margin-bottom: 0.75rem;
  background: linear-gradient(90deg,
    var(--gradient-start),
    var(--gradient-mid),
    var(--gradient-end),
    var(--gradient-start)
  );
  background-size: 300% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 3px;
  display: inline-block;
  text-transform: uppercase;
  animation: gradient-shift 8s infinite linear;
  text-shadow: 0 0 20px rgba(var(--gradient-start-rgb), 0.5);
  position: relative;
}

.app-title::after {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  background: linear-gradient(90deg,
    var(--gradient-start),
    var(--gradient-mid),
    var(--gradient-end),
    var(--gradient-start)
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: blur(10px);
  opacity: 0.7;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

.app-subtitle {
  font-size: 1.4rem;
  color: var(--text-color);
  margin-top: 0;
  font-family: 'Inter', sans-serif;
  letter-spacing: 1.5px;
  font-weight: 300;
  text-transform: uppercase;
  animation: pulse 4s infinite ease-in-out;
  background: rgba(var(--gradient-start-rgb), 0.1);
  padding: 0.5rem 1.5rem;
  border-radius: 30px;
  display: inline-block;
  border: 1px solid rgba(var(--gradient-start-rgb), 0.2);
  backdrop-filter: blur(5px);
}

/* Main layout */
.app-main {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 1.5rem;
}

.chat-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 70px); /* Further increased height */
  width: auto; /* Changed from fixed calculation to auto to adapt to history panel width */
  margin-left: 10px; /* Reduced margin for more space */
  flex: 1; /* Allow it to grow and fill available space */
}

.chat-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  background-color: var(--surface-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 1; /* Ensure this is lower than modal z-index */
  height: calc(100vh - 70px); /* Further increased height by reducing subtraction */
  width: 100%; /* Take full width of parent */
  min-width: 800px; /* Further increased minimum width */
  max-width: 1800px; /* Further increased maximum width */
}

/* Chat header */
.chat-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 1.5rem 2rem; /* Increased horizontal padding */
  background-color: var(--surface-color-2);
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 1rem;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 5;
  height: 70px; /* Fixed height */
  box-sizing: border-box; /* Include padding in height calculation */
}

.chat-domain-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 120px;
}

.domain-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.domain-icon svg {
  color: white;
  stroke: white;
}

.domain-name {
  font-weight: 600;
  font-size: 1.1rem;
}

.chat-provider-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-left: 1.5rem;
  min-width: 120px;
}

.provider-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text-color-secondary);
  margin-right: 0.5rem;
}

.provider-status {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
}

.provider-status.connected {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.provider-status.disconnected {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.provider-status.free-tier {
  background-color: rgba(74, 108, 247, 0.2);
  color: var(--primary-color);
}

/* Chat messages */
.chat-messages {
  flex-grow: 1;
  padding: 1.5rem 2.5rem; /* Further increased horizontal padding */
  overflow-y: auto;
  min-height: 700px; /* Further increased for more content visibility */
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  background-color: var(--surface-color);
  position: absolute;
  top: 70px; /* Position below the header */
  bottom: 120px; /* Leave space for input container */
  left: 0;
  right: 0;
  width: 100%; /* Ensure it takes full width */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* Chat input */
.chat-input-container {
  padding: 1.25rem 2rem; /* Increased padding */
  background-color: var(--surface-color-2);
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 5;
  box-sizing: border-box; /* Include padding in width calculation */
}

.chat-input-wrapper {
  display: flex;
  gap: 0.75rem;
  position: relative;
}

.chat-input {
  flex-grow: 1;
  padding: 1rem 1.25rem;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  background-color: var(--surface-color);
  color: var(--text-color);
  font-size: 1rem;
  resize: none;
  min-height: 24px;
  max-height: 150px;
  outline: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chat-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--gradient-start-rgb), 0.3);
  outline: none;
}

.chat-input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.send-button {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  min-width: 100px;
}

.send-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.send-button:active {
  transform: translateY(1px);
}

.send-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--gradient-start-rgb), 0.5);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.send-text {
  font-weight: 600;
}

.chat-input-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  padding: 0 0.5rem;
}

.input-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.input-status.connected .status-dot {
  background-color: #28a745;
  box-shadow: 0 0 5px #28a745;
}

.input-status.disconnected .status-dot {
  background-color: #dc3545;
  box-shadow: 0 0 5px #dc3545;
}

.domain-badge {
  background-color: var(--surface-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.75rem;
}

/* Controls section */
.controls-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.control-section {
  background-color: var(--surface-color);
  border-radius: 12px;
  padding: 1.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
}

.control-section h2 {
  margin-top: 0;
  color: var(--text-color);
  font-size: 1.2rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Dropdown styles */
.dropdown-container {
  position: relative;
  width: 100%;
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: var(--surface-color-2);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.dropdown-header:hover {
  border-color: var(--primary-color);
}

.dropdown-header:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--gradient-start-rgb), 0.3);
}

.dropdown-selected {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dropdown-color-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.dropdown-icon {
  transition: transform 0.2s ease;
}

.dropdown-open .dropdown-icon {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  width: 100%;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  max-height: 250px;
  overflow-y: auto;
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.dropdown-open .dropdown-menu {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
}

.dropdown-item:hover {
  background-color: var(--surface-color-2);
}

.dropdown-item.active {
  background-color: rgba(74, 108, 247, 0.1);
  color: var(--primary-color);
  font-weight: 500;
}

/* API Key input */
.api-key-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.api-key-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--surface-color-2);
  color: var(--text-color);
  font-size: 1rem;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.api-key-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.api-key-toggle {
  position: absolute;
  right: 10px;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.api-key-toggle:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.api-key-note {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  margin-top: 0.75rem;
}

/* Empty chat state */
.chat-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-color-secondary);
  text-align: center;
  padding: 2rem;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.chat-empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 500;
}

.chat-empty-state p {
  margin: 0;
  font-size: 0.9rem;
}

/* Tutorial mode toggle */
.tutorial-toggle {
  background-color: var(--info-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tutorial-toggle:hover {
  background-color: #149baa;
}

.tutorial-content {
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--surface-color-2);
  border-radius: 8px;
  border-left: 3px solid var(--info-color);
}

.tutorial-item {
  margin-bottom: 1rem;
}

.tutorial-item h3 {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.tutorial-item p {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
  margin: 0;
}

/* Theme selector */
.theme-selector {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 100;
}

.theme-toggle {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.theme-toggle svg {
  stroke: currentColor;
  stroke-width: 2;
  fill: none;
}

.theme-toggle:hover {
  background-color: var(--surface-color-2);
}

.theme-menu {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0.5rem;
  width: 220px;
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
  transition: opacity 0.2s ease, transform 0.2s ease;
  z-index: 101;
  backdrop-filter: blur(10px);
}

.theme-menu-title {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  margin-bottom: 0.5rem;
  padding: 0 0.5rem;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.theme-toggle:hover .theme-menu {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.theme-option {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2px;
}

.theme-option:hover {
  background-color: var(--surface-color-2);
  transform: translateX(2px);
}

.theme-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

.theme-dark .theme-color {
  background-color: #121212;
  border: 1px solid #333;
}

.theme-light .theme-color {
  background-color: #f5f8ff;
  border: 1px solid #ddd;
}

/* Advanced themes option */
.theme-more {
  margin-top: 0.5rem;
  border-top: 1px dashed var(--border-color);
  padding-top: 0.5rem;
  justify-content: space-between;
  font-weight: 500;
  color: var(--primary-color);
}

.theme-more-icon {
  font-size: 1rem;
}

.theme-more-arrow {
  font-size: 0.7rem;
  transition: transform 0.2s ease;
}

.theme-more.active .theme-more-arrow {
  transform: rotate(180deg);
}

.theme-advanced-container {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px dashed var(--border-color);
  max-height: 200px;
  overflow-y: auto;
}

/* New theme colors */
.theme-cosmic .theme-color {
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
}

.theme-aurora .theme-color {
  background: linear-gradient(135deg, #1a2980, #26d0ce);
}

.theme-nebula .theme-color {
  background: linear-gradient(135deg, #4a00e0, #8e2de2);
}

.theme-quantum .theme-color {
  background: linear-gradient(135deg, #134e5e, #71b280);
}

.theme-synthwave .theme-color {
  background: linear-gradient(135deg, #fc28a8, #3d30fa);
}

/* Synthwave theme text readability improvements */
body[data-theme="synthwave"] {
  --text-color: #ffffff;
  --text-color-secondary: #c8c8e0;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999; /* Extremely high z-index to ensure it's above everything */
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-container {
  background-color: var(--surface-color);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--border-color);
}

.tutorial-modal {
  max-width: 700px;
}

.modal-opening {
  opacity: 1;
}

.modal-closing {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
  font-weight: 600;
}

.modal-close {
  background: transparent;
  border: none;
  color: var(--text-color-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: var(--surface-color-2);
  color: var(--text-color);
}

.modal-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--surface-color-2);
}

.modal-tab {
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  color: var(--text-color-secondary);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.modal-tab:hover {
  color: var(--text-color);
}

.modal-tab.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  background-color: var(--surface-color);
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.modal-button {
  padding: 0.6rem 1.25rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-button.primary {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.modal-button.primary:hover {
  background-color: #3a5bd9;
}

.modal-button.danger {
  background-color: #dc3545;
  color: white;
  border: none;
}

.modal-button.danger:hover {
  background-color: #c82333;
}

.modal-button.secondary {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.modal-button.secondary:hover {
  background-color: var(--surface-color-2);
}

.modal-button:focus {
  box-shadow: 0 0 0 3px rgba(var(--gradient-start-rgb), 0.5);
  outline: none;
  border-color: rgba(var(--gradient-start-rgb), 0.8);
}

.modal-button.primary:focus {
  box-shadow: 0 0 0 3px rgba(var(--gradient-start-rgb), 0.5);
}

.modal-button.secondary:focus {
  box-shadow: 0 0 0 3px rgba(var(--text-color-rgb), 0.3);
}

.modal-button.danger:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.5);
}

.modal-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Confirmation Modal */
.confirmation-modal {
  max-width: 450px;
}

.confirmation-message {
  font-size: 1.1rem;
  line-height: 1.5;
  margin: 0.5rem 0;
}

.danger-modal .modal-header {
  border-bottom-color: rgba(220, 53, 69, 0.2);
}

.danger-modal .modal-header h2 {
  color: #dc3545;
}

/* Share Modal */
.share-modal {
  max-width: 600px;
}

.share-description {
  margin-bottom: 1rem;
}

.share-textarea {
  width: 100%;
  height: 200px;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--surface-color-2);
  color: var(--text-color);
  font-family: inherit;
  font-size: 0.9rem;
  resize: none;
  margin-bottom: 1rem;
}

.json-preview {
  width: 100%;
  height: 200px;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--surface-color-2);
  color: var(--text-color);
  font-family: monospace;
  font-size: 0.9rem;
  overflow: auto;
  margin-bottom: 1rem;
  white-space: pre-wrap;
}

.share-options {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1rem;
}

.share-copy-button,
.share-download-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.share-copy-button svg,
.share-download-button svg {
  stroke: currentColor;
}

.modal-input-container {
  margin-bottom: 1rem;
}

.modal-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--surface-color-2);
  color: var(--text-color);
  font-size: 1rem;
}

.modal-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.modal-tab {
  flex: 1;
  padding: 0.75rem;
  text-align: center;
  background: none;
  border: none;
  color: var(--text-color-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.modal-tab.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  background-color: var(--surface-color-2);
}

.provider-info {
  margin-bottom: 1.5rem;
}

.provider-details {
  background-color: var(--surface-color-2);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.provider-detail {
  display: flex;
  margin-bottom: 0.5rem;
}

.provider-detail:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  min-width: 100px;
}

.detail-link {
  color: var(--primary-color);
  text-decoration: none;
}

.detail-link:hover {
  text-decoration: underline;
}

.tutorial-content {
  color: var(--text-color);
}

.tutorial-content h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  color: var(--primary-color);
}

.tutorial-section {
  margin-bottom: 2rem;
}

.tutorial-section h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  color: var(--text-color);
}

.tutorial-section p {
  margin-top: 0;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.tutorial-section ul {
  margin-top: 0.5rem;
  padding-left: 1.5rem;
}

.tutorial-section li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.tutorial-section a {
  color: var(--primary-color);
  text-decoration: none;
}

.tutorial-section a:hover {
  text-decoration: underline;
}

/* Contact form */
.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.contact-item {
  flex: 1;
  min-width: 200px;
}

.contact-form-container {
  background-color: var(--surface-color-2);
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--surface-color);
  color: var(--text-color);
  font-size: 1rem;
  transition: all 0.2s ease;
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.form-error,
.form-success {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.form-error {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.form-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.submit-button {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  margin-top: 0.5rem;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.cancel-button {
  background-color: transparent;
  color: var(--text-color-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: var(--surface-color-2);
  color: var(--text-color);
}

.privacy-note {
  margin-top: 1.5rem;
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  text-align: center;
  padding-top: 1rem;
  border-top: 1px dashed var(--border-color);
}

.privacy-note a {
  color: var(--primary-color);
  text-decoration: none;
}

.privacy-note a:hover {
  text-decoration: underline;
}

.contact-form-intro {
  margin-bottom: 1.5rem;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Free usage status */
.input-status.free-usage {
  color: #f59f00;
}

.input-status.free-usage .status-dot {
  background-color: #f59f00;
  box-shadow: 0 0 5px #f59f00;
}

/* API Key Button */
.api-key-button {
  position: fixed;
  top: 1rem;
  right: 4rem;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 100;
}

.api-key-button:hover {
  background-color: var(--surface-color-2);
  transform: translateY(-2px);
}

.api-key-button svg {
  width: 20px;
  height: 20px;
  color: var(--text-color);
}

.api-key-tooltip {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
  transition: opacity 0.2s ease, transform 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.api-key-button:hover .api-key-tooltip {
  opacity: 1;
  transform: translateY(0);
}

/* Help Button */
.help-button {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  z-index: 100;
  padding: 0; /* Remove padding to ensure perfect circle */
  line-height: 1; /* Ensure text is centered */
}

.help-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

.help-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--gradient-start-rgb), 0.5);
}

.help-tooltip {
  position: absolute;
  top: -40px;
  right: 0;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  transform: translateY(10px);
  pointer-events: none;
  transition: opacity 0.2s ease, transform 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.help-button:hover .help-tooltip {
  opacity: 1;
  transform: translateY(0);
}

/* Footer */
.app-footer {
  margin-top: 2rem;
  text-align: center;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  padding: 1rem 0;
  border-top: 1px solid var(--border-color);
}

/* Message styles */
.message {
  display: flex;
  flex-direction: column;
  max-width: 85%;
  position: relative;
}

.message-content {
  display: flex;
  flex-direction: column;
}

.message-user {
  align-self: flex-end;
}

.message-ai {
  align-self: flex-start;
}

.message-bubble {
  padding: 1rem 1.25rem;
  border-radius: 16px;
  position: relative;
  line-height: 1.5;
}

.message-user .message-bubble {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  border-bottom-right-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.message-ai .message-bubble {
  background-color: var(--surface-color-2);
  border-bottom-left-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.message-time {
  font-size: 0.7rem;
  color: var(--text-color-secondary);
  margin-top: 0.25rem;
  align-self: flex-end;
}

/* API key reminder */
.api-key-reminder {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  background-color: rgba(220, 53, 69, 0.1);
  border-radius: 8px;
  color: #dc3545;
  font-size: 0.9rem;
}

/* Free usage reminder */
.free-usage-reminder {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  background-color: rgba(74, 108, 247, 0.1);
  border-radius: 8px;
  color: var(--primary-color);
  font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-container {
    padding: 1rem;
  }

  .app-title {
    font-size: 2.5rem;
  }

  .controls-container {
    grid-template-columns: 1fr;
  }

  .chat-messages {
    min-height: 300px;
  }
}

/* Animation for loading */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.loading-dots {
  display: inline-flex;
  align-items: center;
  height: 1em;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  margin: 0 2px;
  background-color: currentColor;
  border-radius: 50%;
  display: inline-block;
  animation: pulse 1.5s infinite ease-in-out;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

/* Chat action buttons */
.chat-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-left: auto;
}

.chat-action-button {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0; /* Ensure no padding interferes with icon display */
  overflow: visible; /* Make sure icons aren't clipped */
}

.chat-action-button:hover {
  background-color: var(--surface-color-2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.chat-action-button svg {
  width: 20px;
  height: 20px;
  color: var(--text-color);
  stroke: currentColor;
  stroke-width: 2;
  fill: none;
}

.chat-action-button.active {
  color: white;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.chat-action-button.delete:hover {
  color: white;
  background-color: #dc3545;
  border-color: #dc3545;
}

.chat-input-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.clear-button {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0; /* Ensure no padding interferes with icon display */
  overflow: visible; /* Make sure icons aren't clipped */
}

.clear-button:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border-color: #dc3545;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.clear-button svg {
  width: 20px;
  height: 20px;
  color: var(--text-color);
  stroke: currentColor;
  stroke-width: 2;
  fill: none;
}

.clear-button:hover svg {
  color: #dc3545;
}

.clear-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
  border-color: #dc3545;
}

/* Footer */
.app-footer {
  margin-top: 1rem; /* Further reduced to minimize extra space */
  text-align: center;
  color: var(--text-color);
  font-size: 0.95rem;
  padding: 1rem 0;
  border-top: 1px solid rgba(var(--gradient-start-rgb), 0.2);
  position: relative;
  overflow: hidden;
  background: linear-gradient(to right,
    rgba(var(--gradient-start-rgb), 0.05),
    rgba(var(--gradient-mid-rgb), 0.1),
    rgba(var(--gradient-end-rgb), 0.05)
  );
  border-radius: 0 0 12px 12px;
  margin-bottom: 0;
  width: 100%;
}

.app-footer::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent,
    var(--gradient-start),
    var(--gradient-mid),
    var(--gradient-end),
    transparent
  );
  animation: slide-gradient 8s infinite linear reverse;
  z-index: 1;
}

.app-footer p {
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 1px;
  font-weight: 500;
  margin: 0;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

/* Fine-tuning styles */
.fine-tuning-container {
  background-color: var(--surface-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.fine-tuning-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}

.fine-tuning-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.fine-tuning-description {
  margin: 0.5rem 0 0;
  color: var(--text-color-secondary);
  font-size: 0.95rem;
  flex-basis: 100%;
}

.tutorial-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  color: var(--text-color);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tutorial-button:hover {
  background-color: var(--surface-color-2);
  transform: translateY(-2px);
}

.fine-tuning-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--surface-color-2);
}

.fine-tuning-tab {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--text-color-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.fine-tuning-tab:hover {
  color: var(--text-color);
}

.fine-tuning-tab.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  background-color: var(--surface-color);
}

.fine-tuning-content {
  padding: 1.5rem;
  flex-grow: 1;
  overflow-y: auto;
}

.fine-tuning-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.fine-tuning-error {
  text-align: center;
  padding: 2rem;
  color: var(--danger-color);
}

.fine-tuning-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-section {
  background-color: var(--surface-color-2);
  border-radius: 8px;
  padding: 1.25rem;
  border: 1px solid var(--border-color);
}

.form-section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.form-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.form-group {
  margin-bottom: 1rem;
  flex: 1;
  min-width: 200px;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background-color: var(--surface-color);
  color: var(--text-color);
  font-size: 0.9rem;
}

.form-help {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-color-secondary);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.primary-button {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.25rem;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.primary-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondary-button {
  background-color: var(--surface-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 0.75rem 1.25rem;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-button:hover {
  background-color: var(--surface-color-2);
}

.form-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 6px;
  color: #dc3545;
  margin-bottom: 1rem;
}

.api-key-warning,
.dataset-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 6px;
  color: #ffc107;
  margin-top: 1rem;
}

.job-status-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.no-job-message {
  text-align: center;
  padding: 2rem;
  color: var(--text-color-secondary);
}

.no-job-message svg {
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
}

.job-details {
  width: 100%;
  max-width: 600px;
}

.job-info {
  background-color: var(--surface-color-2);
  border-radius: 8px;
  padding: 1.25rem;
  border: 1px solid var(--border-color);
  margin-top: 1rem;
}

.job-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.job-info-label {
  font-weight: 500;
  color: var(--text-color-secondary);
}

.job-status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
}

.job-status-badge.queued {
  background-color: rgba(108, 117, 125, 0.2);
  color: #6c757d;
}

.job-status-badge.preparing {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.job-status-badge.training {
  background-color: rgba(13, 110, 253, 0.2);
  color: #0d6efd;
}

.job-status-badge.completed {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.job-status-badge.failed {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.job-progress {
  margin: 1.5rem 0;
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: var(--surface-color);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.85rem;
  color: var(--text-color-secondary);
}

.job-message {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: var(--surface-color);
  border-radius: 6px;
  font-size: 0.9rem;
}

.job-completed,
.job-failed,
.job-in-progress {
  margin-top: 1.5rem;
  text-align: center;
  padding: 1rem;
}

.job-completed svg,
.job-failed svg {
  color: #28a745;
  margin-bottom: 0.5rem;
}

.job-failed svg {
  color: #dc3545;
}

.job-note {
  font-size: 0.85rem;
  color: var(--text-color-secondary);
  margin-top: 0.5rem;
}

.models-container {
  padding: 0 0.5rem;
}

.no-models-message {
  text-align: center;
  padding: 2rem;
  color: var(--text-color-secondary);
}

.no-models-message svg {
  margin-bottom: 1rem;
  color: var(--text-color-secondary);
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.model-card {
  background-color: var(--surface-color-2);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all 0.2s ease;
}

.model-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.model-header {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.model-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.model-name {
  font-weight: 600;
  font-size: 1rem;
}

.model-details {
  padding: 1rem;
}

.model-detail {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.detail-label {
  color: var(--text-color-secondary);
}

.model-actions {
  padding: 1rem;
  display: flex;
  gap: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.model-action-button {
  flex: 1;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  color: white;
  border: none;
}

.model-action-button:hover {
  opacity: 0.9;
}

.model-action-button.secondary {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.model-action-button.secondary:hover {
  background-color: var(--surface-color);
}

/* Fine-tuning tutorial styles */
.tutorial-content {
  padding: 0 1rem;
}

.tutorial-section {
  margin-bottom: 2rem;
}

.tutorial-section h4 {
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.tutorial-image {
  margin: 1.5rem 0;
  text-align: center;
}

.tutorial-image img {
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.comparison-table th,
.comparison-table td {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  text-align: left;
}

.comparison-table th {
  background-color: var(--surface-color-2);
  font-weight: 600;
}

.parameter-explanation {
  margin-bottom: 1.5rem;
}

.parameter-explanation h5 {
  margin-bottom: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
}

.technical-note {
  background-color: var(--surface-color-2);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1.5rem;
  border-left: 3px solid var(--primary-color);
}

.technical-note h4 {
  margin-top: 0;
  font-size: 1rem;
}

/* Tab selector styles */
.tab-selector {
  display: flex;
  margin-bottom: 1.5rem;
  background-color: var(--surface-color-2);
  border-radius: 8px;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
}

.tab-button {
  flex: 1;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  border-radius: 6px;
  color: var(--text-color-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.tab-button:hover {
  color: var(--text-color);
}

.tab-button.active {
  background-color: var(--surface-color);
  color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--gradient-start-rgb), 0.3);
}
