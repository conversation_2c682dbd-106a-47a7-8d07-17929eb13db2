"""
VertoAI API Routes
Blueprint initialization
"""

from flask import Blueprint

# Create blueprints
domain_bp = Blueprint('domain', __name__, url_prefix='/api/domain')
inference_bp = Blueprint('inference', __name__, url_prefix='/api/inference')
fine_tuning_bp = Blueprint('fine_tuning', __name__, url_prefix='/api/fine-tuning')

# Import routes to register them with blueprints
from . import domain_routes
from . import inference_routes
from . import fine_tuning_routes
