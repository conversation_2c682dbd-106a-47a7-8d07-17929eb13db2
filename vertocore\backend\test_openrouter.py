"""
Test script for OpenRouter API integration
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from api.services.inference_service import _call_openrouter, _add_domain_context

# Get the project root directory
project_root = Path(__file__).parent.parent

# Load environment variables from the project root .env file
load_dotenv(project_root / '.env')

print(f"Looking for .env file in: {project_root}")

# Get API key from environment
api_key = os.getenv('OPENROUTER_API_KEY')

print(f"Environment variables loaded from: {project_root / '.env'}")
print(f"API key found: {'Yes' if api_key else 'No'}")

if api_key:
    # Print first few characters and mask the rest
    masked_key = api_key[:4] + '*' * (len(api_key) - 4) if len(api_key) > 4 else '****'
    print(f"API key: {masked_key}")
    print(f"API key length: {len(api_key)}")
else:
    print("Error: OPENROUTER_API_KEY not found in environment variables")
    exit(1)

# Test parameters
test_prompt = "What is the capital of France?"
test_domain = "education"
test_model = "google/gemma-3-12b-it:free"
test_max_tokens = 500
test_temperature = 0.7

# Add domain context to the prompt
enhanced_prompt = _add_domain_context(test_prompt, test_domain)
print(f"Enhanced prompt: {enhanced_prompt}")

# Call OpenRouter API
print(f"Calling OpenRouter API with model: {test_model}")
result = _call_openrouter(
    prompt=enhanced_prompt,
    model=test_model,
    api_key=api_key,
    max_tokens=test_max_tokens,
    temperature=test_temperature
)

# Print result
print("\nAPI Response:")
print(f"Text: {result.get('text')}")
print(f"Provider: {result.get('provider')}")
print(f"Model: {result.get('model')}")
print(f"Tokens used: {result.get('tokens_used')}")

if 'error' in result:
    print(f"Error: {result.get('error')}")
else:
    print("\nTest successful! OpenRouter API is working correctly.")
