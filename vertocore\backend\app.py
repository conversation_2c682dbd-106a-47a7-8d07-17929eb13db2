"""
VertoAI Backend Application
Main entry point for the Flask application
"""

from api import create_app
import os
from dotenv import load_dotenv

# Load environment variables from the correct location
from pathlib import Path
env_path = Path(__file__).parent.parent / '.env'
load_dotenv(env_path)

# Create Flask application
app = create_app()

if __name__ == '__main__':
    # Get port from environment or use default
    port = int(os.environ.get('PORT', 5000))

    # Run the application
    app.run(host='0.0.0.0', port=port, debug=os.environ.get('DEBUG', 'False').lower() == 'true')
