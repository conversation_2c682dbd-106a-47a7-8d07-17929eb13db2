"""
Simple test script to check environment variables
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Get the project root directory
project_root = Path(__file__).parent.parent

# Load environment variables from the project root .env file
env_path = project_root / '.env'
print(f"Loading .env from: {env_path.absolute()}")
load_dotenv(env_path)

# Get API key from environment
api_key = os.getenv('OPENROUTER_API_KEY')

print(f"API key found: {'Yes' if api_key else 'No'}")
if api_key:
    print(f"API key first 8 chars: {api_key[:8]}, last 4 chars: {api_key[-4:]}")
