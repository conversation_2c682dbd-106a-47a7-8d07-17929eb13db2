# VertoAI Backend

This is the backend for the VertoAI platform, built with Flask, LangChain, and LangGraph.

## Features

- Flask API for serving the frontend
- LangChain/LangGraph for complex reasoning workflows
- PEFT library for LoRA/QLoRA fine-tuning
- Multiple LLM provider integrations via OpenRouter
- Rate-limiting and fallback logic

## Tech Stack

- Flask for the API
- LangChain/LangGraph for workflows
- PEFT for efficient fine-tuning
- OpenRouter for LLM provider access

## Development

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Start the development server:
```bash
python -m flask run
```

## Project Structure

- `api/routes/` - API endpoints for domain selection, inference, etc.
- `api/services/` - Business logic for fine-tuning, routing, etc.
- `api/__init__.py` - Flask app initialization
- `models/` - LoRA/QLoRA adapters and configs
- `config.py` - Configuration for OpenRouter/API keys

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
OPENAI_API_KEY=your_openai_api_key
OPENROUTER_API_KEY=your_openrouter_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_API_KEY=your_google_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
OPENROUTER_TITLE_API_KEY=your_openrouter_title_api_key
```

Note: API keys are never stored; they are passed directly from the frontend to the backend.
