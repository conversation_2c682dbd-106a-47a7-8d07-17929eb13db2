# VertoAI Tutorial

Welcome to VertoAI, an open-source, multi-domain Gen AI platform that allows you to fine-tune large language models (LLMs) for specific domains like fintech, healthcare, education, and law.

This tutorial is designed for non-technical users who want to understand how to use VertoAI effectively.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Understanding Domains](#understanding-domains)
3. [LLM Providers](#llm-providers)
4. [API Keys](#api-keys)
5. [Fine-Tuning Basics](#fine-tuning-basics)
6. [Advanced Features](#advanced-features)

## Getting Started

When you first open VertoAI, you'll see a simple interface with several sections:

- **Domain Selector**: Choose the specific domain you want to work with
- **LLM Provider**: Select which AI model provider to use
- **API Key**: Enter your API key for the selected provider
- **Tutorial Mode**: Toggle explanations for technical concepts

## Understanding Domains

VertoAI supports multiple domains, each with specialized knowledge:

- **Fintech**: Financial regulations, risk assessment, and investment analysis
- **Healthcare**: Medical diagnostics, treatment recommendations, and health records
- **Education**: Curriculum development, student assessment, and educational content
- **Law**: Legal research, contract analysis, and case summarization

Selecting a domain helps the AI understand the context of your requests and provide more relevant responses.

## LLM Providers

VertoAI supports multiple LLM providers:

- **OpenAI**: Creators of GPT models (requires API key)
- **Google Gemini**: Google's advanced AI models (requires API key)
- **Anthropic Claude**: Known for helpful, harmless, and honest AI (requires API key)
- **DeepSeek**: Specialized in code and technical content (requires API key)

## API Keys

To use VertoAI with external providers, you'll need an API key:

1. Sign up for an account with your chosen provider (OpenAI, Google, etc.)
2. Find the API key section in your account settings
3. Generate a new API key
4. Copy and paste it into VertoAI's API Key field

**Important**: Your API key is never stored on our servers. It's only passed through to the provider when making requests.

## Fine-Tuning Basics

Fine-tuning allows you to customize a general-purpose AI model for your specific needs:

### What is LoRA?

Low-Rank Adaptation (LoRA) is an efficient fine-tuning technique that:

- Trains only a small set of parameters instead of the entire model
- Requires less computational resources
- Results in smaller model files that are easier to distribute

### What is QLoRA?

Quantized Low-Rank Adaptation (QLoRA) combines:

- Quantization: Reducing model precision to 4-bit
- LoRA: Training only a small set of parameters
- Result: Even more efficient fine-tuning with minimal performance loss

### Fine-Tuning Process

1. Select a domain and base model
2. Choose a dataset relevant to your domain
3. Set fine-tuning parameters (or use defaults)
4. Start the fine-tuning process
5. Wait for completion (typically 30-60 minutes)
6. Use your fine-tuned model for domain-specific tasks

## Advanced Features

VertoAI includes several advanced features:

- **LangChain Integration**: Chain prompts for multi-step reasoning
- **LangGraph Workflows**: Create complex decision trees for sophisticated tasks
- **RAG (Retrieval-Augmented Generation)**: Enhance responses with domain-specific knowledge

For more detailed information, please refer to our [technical documentation](./technical-docs.md).
