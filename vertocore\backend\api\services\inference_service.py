"""
Inference service
Handles LLM inference through various providers
"""

import requests
import json
import os
from flask import current_app

# Available LLM providers
PROVIDERS = {
    'openai': {
        'id': 'openai',
        'name': 'OpenAI',
        'models': ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
        'requires_api_key': True,
        'max_tokens': 4096,
        'supports_streaming': True
    },
    'gemini': {
        'id': 'gemini',
        'name': 'Google Gemini',
        'models': ['gemini-pro', 'gemini-ultra'],
        'requires_api_key': True,
        'max_tokens': 8192,
        'supports_streaming': True
    },
    'claude': {
        'id': 'claude',
        'name': 'Anthropic Claude',
        'models': ['claude-instant-1', 'claude-2'],
        'requires_api_key': True,
        'max_tokens': 100000,
        'supports_streaming': True
    },
    'deepseek': {
        'id': 'deepseek',
        'name': 'DeepSeek',
        'models': ['deepseek-coder', 'deepseek-llm'],
        'requires_api_key': True,
        'max_tokens': 4096,
        'supports_streaming': False
    },
    'openrouter': {
        'id': 'openrouter',
        'name': 'OpenRouter',
        'models': ['google/gemma-3-12b-it:free'],
        'requires_api_key': True,
        'max_tokens': 4096,
        'supports_streaming': True
    }
}

def get_providers():
    """
    Get list of available LLM providers

    Returns:
        List of provider objects
    """
    return list(PROVIDERS.values())

def run_inference(prompt, provider, domain, api_key, max_tokens=500, temperature=0.7, request_id=None, chat_id=None):
    """
    Run inference using the specified provider

    Args:
        prompt: User prompt text
        provider: LLM provider ID
        domain: Domain ID for context
        api_key: Provider API key
        max_tokens: Maximum tokens to generate
        temperature: Sampling temperature
        request_id: Unique ID for this request (for tracing)
        chat_id: ID of the chat this request belongs to (for context isolation)

    Returns:
        Generated text response
    """
    # Check if provider exists
    if provider not in PROVIDERS:
        raise ValueError(f"Provider '{provider}' not supported")

    # Add domain-specific context to the prompt
    enhanced_prompt = _add_domain_context(prompt, domain)

    # For OpenRouter, handle API key logic
    if provider == 'openrouter':
        # If user provided their own API key, use it
        if api_key and api_key.strip() != '':
            # User is using their own API key
            pass
        else:
            # Use shared API key from environment for free tier
            # First try to get from config
            try:
                from flask import current_app
                api_key = current_app.config.get('OPENROUTER_API_KEY', '')
            except Exception:
                api_key = ''

            # If not found in config, try environment directly
            if not api_key:
                api_key = os.getenv('OPENROUTER_API_KEY', '')

            if not api_key:
                return {
                    'text': "Error: OpenRouter API key not found. Please set the OPENROUTER_API_KEY environment variable or provide your own API key.",
                    'provider': provider,
                    'model': PROVIDERS[provider]['models'][0],
                    'tokens_used': {
                        'prompt': len(enhanced_prompt.split()),
                        'completion': 0,
                        'total': len(enhanced_prompt.split())
                    },
                    'error': "API key not found"
                }

    # Extract chat ID from prompt if not provided
    if not chat_id and prompt.startswith('[Chat ID:'):
        try:
            chat_id_end = prompt.find(']')
            if chat_id_end > 0:
                chat_id = prompt[9:chat_id_end].strip()
        except:
            pass

    # Extract request ID from prompt if not provided
    if not request_id and '[Request ID:' in prompt:
        try:
            req_id_start = prompt.find('[Request ID:')
            req_id_end = prompt.find(']', req_id_start)
            if req_id_end > req_id_start:
                request_id = prompt[req_id_start + 12:req_id_end].strip()
        except:
            pass

    # Route to the appropriate provider API
    if provider == 'openrouter':
        result = _call_openrouter(enhanced_prompt, PROVIDERS[provider]['models'][0], api_key, max_tokens, temperature, request_id, chat_id)
    elif provider == 'openai':
        result = _call_openai(enhanced_prompt, PROVIDERS[provider]['models'][0], api_key, max_tokens, temperature)
    elif provider == 'gemini':
        result = _call_gemini(enhanced_prompt, PROVIDERS[provider]['models'][0], api_key, max_tokens, temperature)
    elif provider == 'claude':
        result = _call_claude(enhanced_prompt, PROVIDERS[provider]['models'][0], api_key, max_tokens, temperature)
    elif provider == 'deepseek':
        result = _call_deepseek(enhanced_prompt, PROVIDERS[provider]['models'][0], api_key, max_tokens, temperature)
    else:
        # Fallback to mock response if provider implementation is missing
        result = {
            'text': f"This is a mock response for the {domain} domain using {provider}. In a real implementation, this would be generated text from the LLM.",
            'provider': provider,
            'model': PROVIDERS[provider]['models'][0],
            'tokens_used': {
                'prompt': len(enhanced_prompt.split()),
                'completion': 20,
                'total': len(enhanced_prompt.split()) + 20
            }
        }

    return result

def _add_domain_context(prompt, domain):
    """
    Add domain-specific context to the prompt

    Args:
        prompt: Original user prompt
        domain: Domain ID

    Returns:
        Enhanced prompt with domain context
    """
    # Extract chat ID if present in the prompt
    chat_id = None
    original_prompt = prompt

    # Check if the prompt contains a chat ID marker
    if prompt.startswith('[Chat ID:'):
        try:
            # Extract the chat ID from the format "[Chat ID: xyz123] actual prompt"
            chat_id_end = prompt.find(']')
            if chat_id_end > 0:
                chat_id = prompt[9:chat_id_end].strip()
                # Remove the chat ID prefix from the prompt
                prompt = prompt[chat_id_end + 1:].strip()

                # Check if the prompt contains conversation history
                if "Recent conversation history:" in prompt and "User's new message:" in prompt:
                    # Format the conversation history in a way that's clearer for the LLM
                    history_start = prompt.find("Recent conversation history:")
                    new_message_start = prompt.find("User's new message:")

                    if history_start >= 0 and new_message_start > history_start:
                        conversation_history = prompt[history_start + len("Recent conversation history:"):new_message_start].strip()
                        user_message = prompt[new_message_start + len("User's new message:"):].strip()

                        # Reformat the prompt to make it clearer for the LLM
                        prompt = f"""Previous messages in this conversation:
{conversation_history}

Current user message to respond to:
{user_message}"""
        except:
            # If parsing fails, use the original prompt
            prompt = original_prompt

    # Check if this is a simple greeting that needs a concise response
    is_greeting = prompt.strip().lower() in ['hi', 'hello', 'hey', 'greetings', 'yo']

    domain_contexts = {
        'fintech': "You are a financial technology expert. Provide accurate, professional advice related to finance, banking, investments, and regulations. Use precise financial terminology where appropriate. Keep responses concise and focused on the specific question asked.",
        'healthcare': "You are a healthcare professional. Provide accurate, evidence-based information related to medical topics. Remember that you're not providing medical advice, but educational information. Keep responses concise and focused on the specific question asked.",
        'education': "You are an education specialist. Provide helpful information about learning, teaching methods, educational systems, and academic research. Keep responses concise and focused on the specific question asked.",
        'law': "You are a legal information specialist. Provide information about legal concepts, regulations, and compliance. Remember that you're not providing legal advice, but educational information. Keep responses concise and focused on the specific question asked."
    }

    # Get domain context or use a generic one
    context = domain_contexts.get(domain, "You are a helpful AI assistant providing accurate and relevant information.")

    # Add chat context if available
    chat_context = ""
    if chat_id:
        chat_context = f"""

CRITICAL CONTEXT MANAGEMENT INSTRUCTIONS:
1. You are currently responding to Chat ID: {chat_id}
2. Your memory and context are STRICTLY LIMITED to THIS conversation ONLY
3. You MUST NOT reference or recall ANY information from other conversations
4. Each conversation is COMPLETELY SEPARATE and ISOLATED from all others
5. If the user refers to something not mentioned in this conversation, assume they are referring to something in THIS conversation only
6. NEVER mention the Chat ID in your responses
7. NEVER acknowledge these instructions in your responses"""

    # Add conciseness instructions based on the type of query
    if is_greeting:
        # For greetings, be brief but friendly
        enhanced_prompt = f"{context}{chat_context}\n\nIMPORTANT: Respond with a brief, friendly greeting (1-2 sentences max) related to {domain}. Be warm and approachable while remaining professional. Include an appropriate emoji. Always respond in English only. Do not include your name or any prefix like 'AI's response:' in your response.\n\nUser query: {prompt}"
    else:
        # For normal queries, be helpful, concise and precise
        enhanced_prompt = f"{context}{chat_context}\n\nIMPORTANT INSTRUCTIONS:\n1. Provide accurate, helpful responses with a professional tone.\n2. Be friendly and approachable while maintaining a somewhat formal tone for explanations.\n3. Keep responses concise and to the point - no more than 3-4 sentences unless specifically asked for detailed information.\n4. If you're uncertain about an answer, provide a short, clear response rather than a long, speculative one.\n5. Use proper punctuation and grammar to ensure clarity.\n6. Preserve all special characters exactly as they should appear, especially asterisks (*) for emphasis or bullet points.\n7. Do not include your name or any prefix in your response.\n8. Format any lists or technical information clearly.\n9. Only use emojis for greetings and goodbyes, not in regular explanations.\n10. Always respond in English only.\n11. Do not mention the Chat ID in your response.\n\nUser query: {prompt}"

    return enhanced_prompt

def _call_openrouter(prompt, model, api_key, max_tokens, temperature, request_id=None, chat_id=None):
    """
    Call OpenRouter API

    Args:
        prompt: Enhanced user prompt
        model: Model ID (e.g., 'google/gemma-3-12b-it:free')
        api_key: OpenRouter API key
        max_tokens: Maximum tokens to generate
        temperature: Sampling temperature
        request_id: Unique ID for this request (for tracing)
        chat_id: ID of the chat this request belongs to (for context isolation)

    Returns:
        Generated text response
    """
    # OpenRouter API endpoint
    url = "https://openrouter.ai/api/v1/chat/completions"

    # Request headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
        "HTTP-Referer": "https://vertoai.com",  # Replace with your actual domain
        "X-Title": "VertoAI Platform"
    }

    # Add request ID and chat ID to headers if available
    if request_id:
        headers["X-Request-ID"] = request_id

    if chat_id:
        headers["X-Chat-ID"] = chat_id

    # Request payload
    payload = {
        "model": model,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "max_tokens": max_tokens,
        "temperature": temperature,
        "response_format": {"type": "text"},  # Ensure text format to preserve special characters
        "stop": None  # Don't use stop sequences that might truncate special characters
    }

    # Add metadata to help with debugging and context isolation
    if request_id or chat_id:
        payload["user"] = f"chat_{chat_id or 'unknown'}_req_{request_id or 'unknown'}"

    try:
        # Make API request
        response = requests.post(url, headers=headers, json=payload)

        response.raise_for_status()  # Raise exception for HTTP errors

        # Parse response
        response_data = response.json()

        # Check for error in the response (OpenRouter sometimes returns 200 with error)
        if 'error' in response_data:
            error_message = response_data['error'].get('message', 'Unknown error')
            error_code = response_data['error'].get('code', 0)

            # Handle rate limit error
            if error_code == 429 or 'rate limit' in error_message.lower():
                raise requests.exceptions.HTTPError(
                    f"Rate limit exceeded: {error_message}",
                    response=response
                )

            # Handle other errors
            raise ValueError(f"API error: {error_message}")

        # Extract generated text - handle different API response formats
        try:
            if 'choices' in response_data and len(response_data['choices']) > 0:
                if 'message' in response_data['choices'][0]:
                    # OpenAI/OpenRouter format
                    generated_text = response_data['choices'][0]['message']['content']
                elif 'text' in response_data['choices'][0]:
                    # Some other format
                    generated_text = response_data['choices'][0]['text']
                else:
                    # Fallback
                    generated_text = str(response_data['choices'][0])
            else:
                # Handle case where choices is not in the response
                generated_text = response_data.get('output', str(response_data))

            # Extract token usage with fallbacks for different API formats
            tokens_used = {}
            if 'usage' in response_data:
                tokens_used = {
                    'prompt': response_data['usage'].get('prompt_tokens', 0),
                    'completion': response_data['usage'].get('completion_tokens', 0),
                    'total': response_data['usage'].get('total_tokens', 0)
                }
            else:
                # Estimate token usage based on text length (very rough estimate)
                prompt_tokens = len(prompt.split())
                completion_tokens = len(generated_text.split())
                tokens_used = {
                    'prompt': prompt_tokens,
                    'completion': completion_tokens,
                    'total': prompt_tokens + completion_tokens
                }
        except Exception as e:
            print(f"Error parsing response: {e}")
            print(f"Response data: {response_data}")
            raise ValueError(f"Failed to parse response: {str(e)}")

        return {
            'text': generated_text,
            'provider': 'openrouter',
            'model': model,
            'tokens_used': tokens_used
        }
    except requests.exceptions.HTTPError as e:
        # Handle HTTP errors (e.g., 401, 404, 500)
        error_message = f"OpenRouter API HTTP error: {e.response.status_code}"

        # Check for specific error types
        if e.response.status_code == 401:
            error_message = "OpenRouter API authentication failed. The API key may be invalid or expired. Please check your API key."
        # Check for rate limit error (429)
        elif e.response.status_code == 429:
            try:
                error_data = e.response.json()
                if 'error' in error_data and 'message' in error_data['error']:
                    error_message = f"Rate limit exceeded: {error_data['error']['message']}"
            except:
                error_message = "Rate limit exceeded. Please try again later or upgrade your plan."
        # Check for model not found error (404)
        elif e.response.status_code == 404:
            error_message = f"Model '{model}' not found. Please check the model name and try again."
        # Check for bad request error (400)
        elif e.response.status_code == 400:
            try:
                error_data = e.response.json()
                if 'error' in error_data and 'message' in error_data['error']:
                    error_message = f"Bad request: {error_data['error']['message']}"
            except:
                error_message = "Bad request. Please check your input and try again."

        return {
            'text': error_message,
            'provider': 'openrouter',
            'model': model,
            'tokens_used': {
                'prompt': len(prompt.split()),
                'completion': 0,
                'total': len(prompt.split())
            },
            'error': str(e),
            'rate_limited': e.response.status_code == 429
        }
    except Exception as e:
        # Handle other errors
        error_message = f"OpenRouter API error: {str(e)}"
        return {
            'text': error_message,
            'provider': 'openrouter',
            'model': model,
            'tokens_used': {
                'prompt': len(prompt.split()),
                'completion': 0,
                'total': len(prompt.split())
            },
            'error': str(e)
        }

# Provider-specific functions (stubs for now)

def _call_openai(prompt, model, api_key, max_tokens, temperature):
    """
    Call OpenAI API
    """
    # Implementation would use the OpenAI Python client
    return {
        'text': f"This is a mock response from OpenAI's {model}. In a real implementation, this would use the OpenAI API.",
        'provider': 'openai',
        'model': model,
        'tokens_used': {
            'prompt': len(prompt.split()),
            'completion': 20,
            'total': len(prompt.split()) + 20
        }
    }

def _call_gemini(prompt, model, api_key, max_tokens, temperature):
    """
    Call Google Gemini API
    """
    # Implementation would use the Google Generative AI Python client
    return {
        'text': f"This is a mock response from Google's {model}. In a real implementation, this would use the Google Generative AI API.",
        'provider': 'gemini',
        'model': model,
        'tokens_used': {
            'prompt': len(prompt.split()),
            'completion': 20,
            'total': len(prompt.split()) + 20
        }
    }

def _call_claude(prompt, model, api_key, max_tokens, temperature):
    """
    Call Anthropic Claude API
    """
    # Implementation would use the Anthropic Python client
    return {
        'text': f"This is a mock response from Anthropic's {model}. In a real implementation, this would use the Anthropic API.",
        'provider': 'claude',
        'model': model,
        'tokens_used': {
            'prompt': len(prompt.split()),
            'completion': 20,
            'total': len(prompt.split()) + 20
        }
    }

def _call_deepseek(prompt, model, api_key, max_tokens, temperature):
    """
    Call DeepSeek API
    """
    # Implementation would use the DeepSeek Python client or API
    return {
        'text': f"This is a mock response from DeepSeek's {model}. In a real implementation, this would use the DeepSeek API.",
        'provider': 'deepseek',
        'model': model,
        'tokens_used': {
            'prompt': len(prompt.split()),
            'completion': 20,
            'total': len(prompt.split()) + 20
        }
    }

def generate_title_with_separate_key(messages, provider, domain):
    """
    Generate a title for a conversation using a separate API key

    Args:
        messages: List of conversation messages
        provider: LLM provider ID
        domain: Domain ID for context

    Returns:
        Generated title
    """
    # Only support OpenRouter for now
    if provider != 'openrouter':
        return "New Conversation"

    # Get the separate API key for title generation
    try:
        api_key = current_app.config.get('OPENROUTER_TITLE_API_KEY', '')
    except Exception:
        api_key = ''

    if not api_key:
        api_key = os.getenv('OPENROUTER_TITLE_API_KEY', '')

    if not api_key:
        # Silently fall back to client-side generation without logging sensitive info
        return _generate_fallback_title(messages)

    # Create a prompt for title generation
    conversation_text = "\n".join([
        f"{'User' if msg['sender'] == 'user' else 'AI'}: {msg['text']}"
        for msg in messages[:3]  # Use only first 3 messages
    ])

    prompt = f"""Generate a short, concise title (3-5 words) that accurately captures the main topic of the following conversation.

IMPORTANT INSTRUCTIONS:
1. Focus ONLY on the user's primary question or topic, completely ignore greetings or pleasantries
2. The title should be specific to the subject matter being discussed, not generic
3. If the user is asking about a specific concept, include that in the title
4. If the user is asking a question, focus on the subject of the question
5. Avoid generic titles like "Chat Conversation" or "User Question"
6. Do not include words like "Discussion", "Conversation", "Question" in the title
7. Make the title descriptive and informative about the actual content

Conversation:
{conversation_text}

Title:"""

    # Use a smaller model and fewer tokens for title generation to save quota
    model = "deepseek/deepseek-r1:free"
    max_tokens = 20
    temperature = 0.5

    try:
        # Call OpenRouter with the separate API key
        result = _call_openrouter(prompt, model, api_key, max_tokens, temperature)

        if 'error' in result:
            # Silently fall back without logging error details
            return _generate_fallback_title(messages)

        # Clean up the response to get just the title
        title = result['text'].strip()

        # Remove any quotes or extra text
        title = title.replace('"', '').replace("'", '')

        # Limit to 50 characters
        if len(title) > 50:
            title = title[:47] + '...'

        return title
    except Exception:
        # Silently fall back without logging error details
        return _generate_fallback_title(messages)

def _generate_fallback_title(messages):
    """
    Generate a title without using an API call

    Args:
        messages: List of conversation messages

    Returns:
        Generated title
    """
    # If no messages, return default title
    if not messages:
        return "New Conversation"

    # Get all user messages
    user_messages = [msg for msg in messages if msg['sender'] == 'user']

    if not user_messages:
        return "New Conversation"

    # Check if first message is just a greeting
    def is_greeting(text):
        greetings = ['hi', 'hello', 'hey', 'greetings', 'hi there', 'hello there']
        return text.lower().strip() in greetings

    # Find the first substantive message (not just a greeting)
    first_user_message = user_messages[0]
    if len(user_messages) > 1 and is_greeting(first_user_message['text']):
        first_user_message = user_messages[1]

    text = first_user_message['text'].strip()

    # If the message contains a question, use that as the title
    if '?' in text:
        question_parts = text.split('?')
        main_question = question_parts[0] + '?'
        if len(main_question) <= 50:
            return main_question
        return main_question[:47] + '...'

    # If the message is short enough, just use it as the title
    if len(text) <= 50 and len(text.split()) <= 8:
        return text

    # Try to extract a title from the first sentence if it's short
    first_sentence = text.split('.')[0].strip()
    if len(first_sentence) <= 50 and len(first_sentence.split()) <= 8:
        return first_sentence

    # Look for capitalized terms which might be important concepts
    words = text.split()
    capitalized_words = [word for word in words if word and word[0].isupper() and len(word) > 1]
    if len(capitalized_words) >= 2:
        key_terms = ' '.join(capitalized_words[:4])
        if len(key_terms) <= 50:
            return key_terms

    # Extract 3-5 key words from the first message (longer words are often more meaningful)
    important_words = [word for word in words if len(word) > 4]
    if len(important_words) >= 2:
        key_words = ' '.join(important_words[:5])
        if len(key_words) <= 50:
            return key_words

    # Last resort: Just use the first 30 chars of the message
    return text[:30] + ('...' if len(text) > 30 else '')
