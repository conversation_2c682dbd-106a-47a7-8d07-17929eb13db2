import React from 'react';

const ThreeBackground = ({ theme }) => {
  // Get theme color
  let gradientColors;
  switch (theme) {
    case 'light':
      gradientColors = 'linear-gradient(135deg, #4a6cf7, #6e48e6)';
      break;
    case 'cosmic':
      gradientColors = 'linear-gradient(135deg, #7303c0, #ec38bc)';
      break;
    case 'aurora':
      gradientColors = 'linear-gradient(135deg, #5390d9, #48bfe3)';
      break;
    case 'nebula':
      gradientColors = 'linear-gradient(135deg, #9333ea, #c084fc)';
      break;
    case 'quantum':
      gradientColors = 'linear-gradient(135deg, #059669, #34d399)';
      break;
    case 'synthwave':
      gradientColors = 'linear-gradient(135deg, #ff0080, #7928ca)';
      break;
    default:
      gradientColors = 'linear-gradient(135deg, #4a6cf7, #9333ea)';
  }

  return (
    <div
      className="background-canvas"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        zIndex: -1,
        background: gradientColors,
        opacity: 0.7,
        pointerEvents: 'none',
        overflow: 'hidden',
        filter: 'blur(100px)'
      }}
    />
  );
};

export default ThreeBackground;
