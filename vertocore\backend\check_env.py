"""
Check if environment variables are properly loaded
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Get the project root directory
project_root = Path(__file__).parent.parent

# Load environment variables from the project root .env file
load_dotenv(project_root / '.env')

print(f"Looking for .env file in: {project_root}")

# Check if API key is loaded
api_key = os.getenv('OPENROUTER_API_KEY')
if api_key:
    # Print first few characters and mask the rest
    masked_key = api_key[:4] + '*' * (len(api_key) - 4) if len(api_key) > 4 else '****'
    print(f"OPENROUTER_API_KEY is set: {masked_key}")
    print(f"Length of API key: {len(api_key)}")
else:
    print("OPENROUTER_API_KEY is not set!")

# Check other environment variables
print("\nOther environment variables:")
for var in ['DEBUG', 'SECRET_KEY', 'PORT', 'CORS_ORIGINS']:
    value = os.getenv(var)
    print(f"{var}: {value if value else 'Not set'}")
