import React from 'react';
import Dropdown from './Dropdown';

const ProviderSelector = ({ selectedProvider, onProviderChange }) => {
  const providers = [
    { id: 'openai', label: 'OpenAI' },
    { id: 'gemini', label: '<PERSON>' },
    { id: 'claude', label: '<PERSON>' },
    { id: 'openrouter', label: 'OpenRouter' },
    { id: 'deepseek', label: 'DeepSeek' }
  ];

  return (
    <div className="control-section">
      <h2>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 2a10 10 0 1 0 10 10H12V2z"></path>
          <path d="M12 2a10 10 0 0 1 10 10h-10V2z"></path>
          <path d="M12 12l-8 8"></path>
          <path d="M12 12l8 8"></path>
        </svg>
        LLM Provider
      </h2>
      <Dropdown
        options={providers}
        selectedValue={selectedProvider}
        onSelect={onProviderChange}
        placeholder="Select a provider"
      />
    </div>
  );
};

export default ProviderSelector;
