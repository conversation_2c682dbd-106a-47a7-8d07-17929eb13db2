"""
Test script to check OpenRouter API using the OpenAI SDK
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Get the project root directory
project_root = Path(__file__).parent.parent

# Load environment variables from the project root .env file
env_path = project_root / '.env'
print(f"Loading .env from: {env_path.absolute()}")
load_dotenv(env_path)

# Get API key from environment
api_key = os.getenv('OPENROUTER_API_KEY')

print(f"API key found: {'Yes' if api_key else 'No'}")
if api_key:
    print(f"API key first 8 chars: {api_key[:8]}, last 4 chars: {api_key[-4:]}")

# Test OpenRouter API using OpenAI SDK
try:
    from openai import OpenAI

    # Initialize the client with OpenRouter base URL
    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=api_key,
        default_headers={
            "HTTP-Referer": "https://vertoai.com",
            "X-Title": "VertoAI Platform"
        }
    )

    print("\nTesting OpenRouter API using OpenAI SDK...")

    # Make the API call
    response = client.chat.completions.create(
        model="qwen/qwen3-32b:free",
        messages=[
            {"role": "user", "content": "Say hello"}
        ],
        max_tokens=50,
        temperature=0.7
    )

    print(f"Response: {response}")

except ImportError:
    print("OpenAI SDK not installed. Run 'pip install openai' to install it.")
except Exception as e:
    print(f"Error: {e}")
