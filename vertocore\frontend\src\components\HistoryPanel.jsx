import React, { useState, useRef, useEffect } from 'react';
import TypewriterText from './TypewriterText';

const HistoryPanel = ({
  chatHistory,
  archivedChats,
  onSelectChat,
  onNewChat,
  onArchiveChat,
  onUnarchiveChat,
  onDeleteChat,
  onPinChat,
  currentChatId,
  onShowDeleteModal,
  onShowArchiveModal,
  onDeleteAllChats,
  selectedDomain
}) => {
  const [activeTab, setActiveTab] = useState('recent');
  const [searchTerm, setSearchTerm] = useState('');
  const [isResizing, setIsResizing] = useState(false);
  const panelRef = useRef(null);
  const initialX = useRef(0);
  const initialWidth = useRef(0);

  // Handle resize start
  const handleResizeStart = (e) => {
    e.preventDefault();
    setIsResizing(true);
    initialX.current = e.clientX;
    initialWidth.current = panelRef.current.offsetWidth;

    // Add event listeners for resize
    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', handleResizeEnd);
  };

  // Handle resize
  const handleResize = (e) => {
    if (!isResizing) return;

    const newWidth = initialWidth.current + (e.clientX - initialX.current);
    // Constrain width between 250px and 500px
    const constrainedWidth = Math.max(250, Math.min(500, newWidth));

    if (panelRef.current) {
      panelRef.current.style.width = `${constrainedWidth}px`;
    }
  };

  // Handle resize end
  const handleResizeEnd = () => {
    setIsResizing(false);
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', handleResizeEnd);
  };

  // Clean up event listeners on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleResize);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [isResizing]);

  // Filter and sort chats based on domain, search term and pinned status
  const filterAndSortChats = (chats) => {
    // First filter by domain
    let filtered = chats.filter(chat =>
      !chat.domain || chat.domain === selectedDomain
    );

    // Then filter by search term if one exists
    if (searchTerm.trim()) {
      filtered = filtered.filter(chat =>
        chat.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        chat.lastMessage?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Then sort: pinned chats first, then by date (newest first)
    return filtered.sort((a, b) => {
      // Pinned chats always come first
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // If both are pinned or both are not pinned, sort by date (newest first)
      return new Date(b.date) - new Date(a.date);
    });
  };

  const filteredHistory = filterAndSortChats(chatHistory);
  const filteredArchived = filterAndSortChats(archivedChats);

  // Handle delete confirmation
  const handleDeleteClick = (e, chat) => {
    e.stopPropagation();
    onShowDeleteModal(chat);
  };

  // Handle archive confirmation
  const handleArchiveClick = (e, chat) => {
    e.stopPropagation();
    onShowArchiveModal(chat);
  };

  return (
    <div className={`history-panel ${isResizing ? 'resizing' : ''}`} ref={panelRef}>
      {/* Resize handle */}
      <div
        className="resize-handle"
        onMouseDown={handleResizeStart}
        title="Drag to resize"
      >
        <div className="resize-handle-line"></div>
      </div>

      {/* Header - Grid Row 1 */}
      <div className="history-header">
        <h2 style={{ marginLeft: '0.5rem' }}>Chat History</h2>
        <div className="history-buttons">
          <div
            className="new-chat-button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onNewChat();
            }}
            title="Start a new chat"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            <span>New Chat</span>
          </div>
          <button
            className="delete-all-chats-button"
            onClick={onDeleteAllChats}
            title="Delete all chats"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="3 6 5 6 21 6"></polyline>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
              <line x1="10" y1="11" x2="10" y2="17"></line>
              <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
          </button>
        </div>
      </div>

      {/* Search Bar - Grid Row 2 */}
      <div className="history-search">
        <input
          type="text"
          placeholder="Search conversations..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="history-search-input"
        />
        {searchTerm && (
          <button
            className="clear-search-button"
            onClick={() => setSearchTerm('')}
            title="Clear search"
          >
            ×
          </button>
        )}
      </div>

      {/* Tabs - Grid Row 3 */}
      <div className="history-tabs">
        <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-start' }}>
          <button
            className={`history-tab ${activeTab === 'recent' ? 'active' : ''}`}
            onClick={() => setActiveTab('recent')}
          >
            Recent
          </button>
          <button
            className={`history-tab ${activeTab === 'archived' ? 'active' : ''}`}
            onClick={() => setActiveTab('archived')}
          >
            Archived
          </button>
        </div>
      </div>

      {/* Scrollable Content - Grid Row 4 */}
      <div className="history-list">
        {activeTab === 'recent' ? (
          filteredHistory.length > 0 ? (
            filteredHistory.map(chat => (
              <ChatHistoryItem
                key={chat.id}
                chat={chat}
                isActive={chat.id === currentChatId}
                onSelect={() => onSelectChat(chat.id)}
                onArchive={() => onArchiveChat(chat.id)}
                onDelete={() => onDeleteChat(chat.id)}
                onPin={() => onPinChat(chat.id)}
                handleArchiveClick={handleArchiveClick}
                handleDeleteClick={handleDeleteClick}
              />
            ))
          ) : (
            <div className="empty-history-message">
              {searchTerm ? 'No chats match your search' : 'No recent conversations'}
            </div>
          )
        ) : (
          filteredArchived.length > 0 ? (
            filteredArchived.map(chat => (
              <ChatHistoryItem
                key={chat.id}
                chat={chat}
                isActive={chat.id === currentChatId}
                isArchived={true}
                onSelect={() => onSelectChat(chat.id)}
                onUnarchive={() => onUnarchiveChat(chat.id)}
                onDelete={() => onDeleteChat(chat.id)}
                handleDeleteClick={handleDeleteClick}
              />
            ))
          ) : (
            <div className="empty-history-message">
              {searchTerm ? 'No archived chats match your search' : 'No archived conversations'}
            </div>
          )
        )}
      </div>
    </div>
  );
};

// Chat history item component
const ChatHistoryItem = ({
  chat,
  isActive,
  isArchived = false,
  onSelect,
  onArchive,
  onUnarchive,
  onDelete,
  onPin,
  handleArchiveClick,
  handleDeleteClick
}) => {
  const [showActions, setShowActions] = useState(false);
  const [titleAnimationComplete, setTitleAnimationComplete] = useState(false);

  // Check if this is a newly generated title that needs animation
  const needsAnimation = chat.titleIsNew === true;

  const formatDate = (date) => {
    const now = new Date();
    const chatDate = new Date(date);

    // If today, show time
    if (chatDate.toDateString() === now.toDateString()) {
      return chatDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // If this year, show month and day
    if (chatDate.getFullYear() === now.getFullYear()) {
      return chatDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }

    // Otherwise show date with year
    return chatDate.toLocaleDateString([], { month: 'short', day: 'numeric', year: 'numeric' });
  };

  return (
    <div
      className={`history-item ${isActive ? 'active' : ''} ${chat.isPinned ? 'pinned' : ''} ${chat.hasUnread ? 'has-unread' : ''}`}
      onClick={onSelect}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="history-item-content">
        <div className="history-item-title">
          {chat.isPinned && (
            <span className="pin-indicator" title="Pinned chat">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
            </span>
          )}
          {chat.hasUnread && (
            <span className="unread-indicator" title="New messages">
              <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="currentColor" stroke="none">
                <circle cx="12" cy="12" r="10"></circle>
              </svg>
            </span>
          )}
          {needsAnimation && !titleAnimationComplete ? (
            <TypewriterText
              text={chat.title}
              speed={30}
              onComplete={() => setTitleAnimationComplete(true)}
            />
          ) : (
            <span>{chat.title}</span>
          )}
        </div>
        <div className="history-item-preview">{chat.lastMessage}</div>
        <div className="history-item-date">{formatDate(chat.date)}</div>
      </div>

      {showActions && (
        <div className="history-item-actions">
          {!isArchived ? (
            <>
              <button
                className="history-action-button"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onPin) onPin();
                }}
                title={chat.isPinned ? "Unpin chat" : "Pin chat"}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                  <line x1="9" y1="9" x2="15" y2="9"></line>
                  <line x1="9" y1="13" x2="15" y2="13"></line>
                </svg>
              </button>
              {handleArchiveClick && (
                <button
                  className="history-action-button"
                  onClick={(e) => handleArchiveClick(e, chat)}
                  title="Archive chat"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="21 8 21 21 3 21 3 8"></polyline>
                    <rect x="1" y="3" width="22" height="5"></rect>
                    <line x1="10" y1="12" x2="14" y2="12"></line>
                  </svg>
                </button>
              )}
            </>
          ) : (
            <button
              className="history-action-button"
              onClick={(e) => {
                e.stopPropagation();
                if (onUnarchive) onUnarchive();
              }}
              title="Unarchive chat"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="21 8 21 21 3 21 3 8"></polyline>
                <rect x="1" y="3" width="22" height="5"></rect>
                <line x1="10" y1="12" x2="14" y2="12"></line>
                <line x1="12" y1="10" x2="12" y2="14"></line>
              </svg>
            </button>
          )}
          {handleDeleteClick && (
            <button
              className="history-action-button delete"
              onClick={(e) => { handleDeleteClick(e, chat); }}
              title="Delete chat"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="3 6 5 6 21 6"></polyline>
                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
              </svg>
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default HistoryPanel;
