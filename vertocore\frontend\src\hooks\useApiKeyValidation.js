import { useState, useEffect } from 'react';

/**
 * Custom hook for validating API keys
 * @param {string} apiKey - The API key to validate
 * @param {string} provider - The LLM provider (openai, gemini, claude, deepseek)
 * @returns {Object} - Validation state and functions
 */
const useApiKeyValidation = (apiKey, provider) => {
  const [isValid, setIsValid] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState(null);

  // Validate API key when it changes or provider changes
  useEffect(() => {
    if (!apiKey) {
      setIsValid(false);
      setError(null);
      return;
    }

    const validateApiKey = async () => {
      setIsValidating(true);
      setError(null);

      try {
        // In a real implementation, we would make an API call to validate the key
        // For now, we'll just simulate validation with basic checks
        
        // Simple validation rules (in a real app, we'd call the provider's API)
        let valid = false;
        
        if (provider === 'openai' && apiKey.startsWith('sk-')) {
          valid = apiKey.length > 20;
        } else if (provider === 'gemini' && apiKey.length > 10) {
          valid = true;
        } else if (provider === 'claude' && apiKey.length > 10) {
          valid = true;
        } else if (provider === 'deepseek' && apiKey.length > 10) {
          valid = true;
        }
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setIsValid(valid);
        
        if (!valid) {
          setError(`Invalid API key format for ${provider}`);
        }
      } catch (err) {
        setIsValid(false);
        setError(err.message || 'Failed to validate API key');
      } finally {
        setIsValidating(false);
      }
    };

    validateApiKey();
  }, [apiKey, provider]);

  return {
    isValid,
    isValidating,
    error,
    // Function to manually trigger validation
    validate: () => {
      if (!apiKey) {
        setIsValid(false);
        setError('API key is required');
        return false;
      }
      
      // Re-run the effect
      const validateApiKey = async () => {
        setIsValidating(true);
        setError(null);
        
        try {
          // Simple validation as above
          let valid = false;
          
          if (provider === 'openai' && apiKey.startsWith('sk-')) {
            valid = apiKey.length > 20;
          } else if (provider === 'gemini' && apiKey.length > 10) {
            valid = true;
          } else if (provider === 'claude' && apiKey.length > 10) {
            valid = true;
          } else if (provider === 'deepseek' && apiKey.length > 10) {
            valid = true;
          }
          
          await new Promise(resolve => setTimeout(resolve, 500));
          
          setIsValid(valid);
          
          if (!valid) {
            setError(`Invalid API key format for ${provider}`);
          }
          
          return valid;
        } catch (err) {
          setIsValid(false);
          setError(err.message || 'Failed to validate API key');
          return false;
        } finally {
          setIsValidating(false);
        }
      };
      
      return validateApiKey();
    }
  };
};

export default useApiKeyValidation;
