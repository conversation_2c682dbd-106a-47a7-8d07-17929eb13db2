"""
Simple test script to check OpenRouter API
"""

import os
import requests
from pathlib import Path
from dotenv import load_dotenv

# Get the project root directory
project_root = Path(__file__).parent.parent

# Load environment variables from the project root .env file
env_path = project_root / '.env'
print(f"Loading .env from: {env_path.absolute()}")
load_dotenv(env_path)

# Get API key from environment
api_key = os.getenv('OPENROUTER_API_KEY')

print(f"API key found: {'Yes' if api_key else 'No'}")
if api_key:
    print(f"API key first 8 chars: {api_key[:8]}, last 4 chars: {api_key[-4:]}")

# Test OpenRouter API
url = "https://openrouter.ai/api/v1/chat/completions"
    
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {api_key}",
    "HTTP-Referer": "https://vertoai.com",
    "X-Title": "VertoAI Platform"
}

payload = {
    "model": "qwen/qwen3-32b:free",
    "messages": [
        {"role": "user", "content": "Say hello"}
    ],
    "max_tokens": 50,
    "temperature": 0.7
}

print("\nTesting OpenRouter API...")
print(f"URL: {url}")
print(f"Headers: {headers}")

try:
    response = requests.post(url, headers=headers, json=payload)
    print(f"Response status code: {response.status_code}")
    print(f"Response text: {response.text[:200]}...")
except Exception as e:
    print(f"Error: {e}")
