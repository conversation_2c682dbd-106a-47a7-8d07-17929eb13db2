import React, { useState } from 'react';

const ThemeSelector = ({ currentTheme, onThemeChange }) => {
  const [showMoreThemes, setShowMoreThemes] = useState(false);

  const basicThemes = [
    { id: 'dark', label: 'Dark' },
    { id: 'light', label: 'Light' }
  ];

  const advancedThemes = [
    { id: 'cosmic', label: 'Cosmic Void', color: 'linear-gradient(135deg, #0f0c29, #302b63, #24243e)' },
    { id: 'aurora', label: 'Aurora Borealis', color: 'linear-gradient(135deg, #1a2980, #26d0ce)' },
    { id: 'nebula', label: 'Nebula Dust', color: 'linear-gradient(135deg, #4a00e0, #8e2de2)' },
    { id: 'quantum', label: 'Quantum Field', color: 'linear-gradient(135deg, #134e5e, #71b280)' },
    { id: 'synthwave', label: 'Synthwave', color: 'linear-gradient(135deg, #fc28a8, #7638fa, #3d30fa)' }
  ];

  // Get current theme icon and label
  const getCurrentThemeInfo = () => {
    switch (currentTheme) {
      case 'light':
        return {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
          ),
          label: 'Light'
        };
      case 'cosmic':
        return {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="4"></circle>
              <line x1="21.17" y1="8" x2="12" y2="8"></line>
              <line x1="3.95" y1="6.06" x2="8.54" y2="14"></line>
              <line x1="10.88" y1="21.94" x2="15.46" y2="14"></line>
            </svg>
          ),
          label: 'Cosmic Void'
        };
      case 'aurora':
        return {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M2 15h20"></path>
              <path d="M5 21h14"></path>
              <path d="M9.17 3.59a6 6 0 0 1 5.66 0"></path>
              <path d="M6.08 7.11a10 10 0 0 1 11.84 0"></path>
              <path d="M3.03 10.71a14 14 0 0 1 17.94 0"></path>
            </svg>
          ),
          label: 'Aurora Borealis'
        };
      case 'nebula':
        return {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
              <path d="M19 3v4"></path>
              <path d="M21 5h-4"></path>
            </svg>
          ),
          label: 'Nebula Dust'
        };
      case 'quantum':
        return {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 3a9 9 0 1 0 9 9"></path>
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M12 12h9"></path>
              <path d="M12 9v6"></path>
            </svg>
          ),
          label: 'Quantum Field'
        };
      case 'synthwave':
        return {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M2 18h1.4c1.3 0 2.5-.7 3.2-1.8l.2-.3c.7-1.1 1.9-1.8 3.2-1.8s2.5.7 3.2 1.8l.2.3c.7 1.1 1.9 1.8 3.2 1.8H18"></path>
              <path d="m4 14 .7-1.2.2-.3C5.5 11.5 6.7 11 8 11s2.5.6 3.2 1.6l.2.3c.7 1.1 1.9 1.7 3.2 1.7s2.5-.7 3.2-1.8l.2-.3c.7-1.1 1.9-1.8 3.2-1.8H22"></path>
              <path d="m2 10 .7-1.2.2-.3C3.5 7.5 4.7 7 6 7s2.5.6 3.2 1.6l.2.3c.7 1.1 1.9 1.7 3.2 1.7s2.5-.7 3.2-1.8l.2-.3C16.5 7.5 17.7 7 19 7h3"></path>
              <path d="M4 6h16"></path>
            </svg>
          ),
          label: 'Synthwave'
        };
      case 'dark':
      default:
        return {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
            </svg>
          ),
          label: 'Dark'
        };
    }
  };

  const toggleMoreThemes = () => {
    setShowMoreThemes(!showMoreThemes);
  };

  const { icon, label } = getCurrentThemeInfo();

  return (
    <div className="theme-selector">
      <div className="theme-toggle">
        <span>{icon}</span>

        <div className="theme-menu">
          <div className="theme-menu-title">Theme</div>

          {/* Basic themes */}
          {basicThemes.map(theme => (
            <div
              key={theme.id}
              className={`theme-option theme-${theme.id}`}
              onClick={() => onThemeChange(theme.id)}
            >
              <span className="theme-color"></span>
              <span>{theme.label}</span>
              {currentTheme === theme.id && <span>✓</span>}
            </div>
          ))}

          {/* Explore more themes option */}
          <div
            className={`theme-option theme-more ${showMoreThemes ? 'active' : ''}`}
            onClick={toggleMoreThemes}
          >
            <span className="theme-more-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="m16 12-4-4-4 4"></path>
                <path d="m8 12 4 4 4-4"></path>
              </svg>
            </span>
            <span>Explore more themes...</span>
            <span className="theme-more-arrow">{showMoreThemes ? '▲' : '▼'}</span>
          </div>

          {/* Advanced themes (shown when "Explore more themes" is clicked) */}
          {showMoreThemes && (
            <div className="theme-advanced-container">
              {advancedThemes.map(theme => (
                <div
                  key={theme.id}
                  className={`theme-option theme-${theme.id}`}
                  onClick={() => onThemeChange(theme.id)}
                >
                  <span
                    className="theme-color"
                    style={{ background: theme.color }}
                  ></span>
                  <span>{theme.label}</span>
                  {currentTheme === theme.id && <span>✓</span>}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ThemeSelector;
