import React, { useState, useEffect } from 'react';

/**
 * TypewriterText component that animates text with a typewriter effect
 * 
 * @param {string} text - The text to animate
 * @param {number} speed - The typing speed in milliseconds per character
 * @param {function} onComplete - Callback function when animation completes
 * @returns {JSX.Element} - The animated text component
 */
const TypewriterText = ({ text, speed = 50, onComplete = () => {} }) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    // Reset when text changes
    setDisplayText('');
    setCurrentIndex(0);
    setIsComplete(false);
  }, [text]);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    } else if (!isComplete) {
      setIsComplete(true);
      onComplete();
    }
  }, [currentIndex, text, speed, isComplete, onComplete]);

  return <span>{displayText}</span>;
};

export default TypewriterText;
