# VertoAI Platform

VertoAI is a simplified multi-domain Gen AI platform that allows users to interact with various LLM providers through a clean, intuitive interface.

## Project Overview

This project implements a multi-domain Gen AI platform with the following components:

- **Frontend**: React + Tailwind CSS for a clean, responsive UI
- **Backend**: Flask API for handling requests to LLM providers
- **LLM Integration**: Support for multiple providers (OpenRouter, OpenAI, Gemini, Claude, DeepSeek)
- **Domain Specialization**: Focused on fintech, healthcare, education, and law domains
- **Free Usage**: 10 free prompts per day using OpenRouter's free models

## Simplified Approach

This implementation focuses on core functionality with a simplified UI:

1. **Streamlined UI**: Clean interface without complex 3D visualizations
2. **Modal Improvements**: Properly positioned modal windows for better UX
3. **Background Effects**: Simple gradient backgrounds instead of complex Three.js effects
4. **Core Functionality**: Focus on domain selection, provider selection, and chat interface

## Getting Started

### Prerequisites
- Node.js (v16+)
- Python (v3.9+)
- API keys for LLM providers

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/vertoai.git
cd vertoai
```

2. Set up the frontend:
```bash
cd vertocore/frontend
npm install
npm run dev
```

3. Set up the backend:
```bash
cd vertocore/backend
pip install -r requirements.txt
python -m flask run
```

4. Copy the `.env.example` file to `.env` and fill in your API keys:
```bash
cp .env.example .env
```

## Usage

1. Open your browser and navigate to `http://localhost:5173`
2. Select a domain (fintech, healthcare, education, or law)
3. Choose an LLM provider:
   - **OpenRouter**: Default provider with free usage (10 prompts/day)
   - **OpenAI**: Requires your own API key
   - **Gemini**: Requires your own API key
   - **Claude**: Requires your own API key
   - **DeepSeek**: Requires your own API key
4. Enter your API key if using a provider other than OpenRouter's free tier
5. Start using the platform for domain-specific AI tasks

## Free Usage with OpenRouter

VertoAI comes with 10 free prompts per day using OpenRouter's free models:
- Default model: `google/gemma-3-12b-it:free`
- No API key required for the free tier
- Usage resets every 24 hours

## License

This project is licensed under the MIT License - see the LICENSE file for details.
