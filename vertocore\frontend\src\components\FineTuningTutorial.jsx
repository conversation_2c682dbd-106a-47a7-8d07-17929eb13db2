import React, { useState, useRef, useEffect } from 'react';

const FineTuningTutorial = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const modalRef = useRef(null);

  // <PERSON>le click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      // Prevent scrolling when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      // Restore scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  // State for tracking modal closing animation
  const [isClosing, setIsClosing] = useState(false);

  // Handle modal close with animation
  const handleClose = () => {
    setIsClosing(true);
    if (modalRef.current) {
      setTimeout(() => {
        onClose();
        setIsClosing(false);
      }, 300); // Match this with CSS transition duration
    } else {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" style={{ zIndex: 10000 }}>
      <div
        className={`modal-container tutorial-modal ${isClosing ? 'modal-closing' : 'modal-opening'}`}
        ref={modalRef}
        style={{ position: 'relative', zIndex: 10001 }}
      >
        <div className="modal-header">
          <h2>Fine-Tuning Tutorial</h2>
          <button className="modal-close" onClick={handleClose}>×</button>
        </div>

        <div className="modal-tabs">
          <button
            className={`modal-tab ${activeTab === 'overview' ? 'active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`modal-tab ${activeTab === 'lora' ? 'active' : ''}`}
            onClick={() => setActiveTab('lora')}
          >
            LoRA & QLoRA
          </button>
          <button
            className={`modal-tab ${activeTab === 'parameters' ? 'active' : ''}`}
            onClick={() => setActiveTab('parameters')}
          >
            Parameters
          </button>
          <button
            className={`modal-tab ${activeTab === 'workflow' ? 'active' : ''}`}
            onClick={() => setActiveTab('workflow')}
          >
            Workflow
          </button>
        </div>

        <div className="modal-body">
          {activeTab === 'overview' && (
            <div className="tutorial-content">
              <h3>What is Fine-Tuning?</h3>
              
              <div className="tutorial-section">
                <p>
                  Fine-tuning is the process of taking a pre-trained language model and further training it on a specific dataset to adapt it for particular tasks or domains.
                </p>
                
                <div className="tutorial-image">
                  <img src="https://i.imgur.com/XYZ123.png" alt="Fine-tuning diagram" />
                </div>
                
                <h4>Benefits of Fine-Tuning</h4>
                <ul>
                  <li><strong>Domain Specialization:</strong> Models become experts in specific fields like finance, healthcare, or law</li>
                  <li><strong>Better Performance:</strong> Fine-tuned models outperform general models on domain-specific tasks</li>
                  <li><strong>Reduced Hallucinations:</strong> Models are less likely to make up incorrect information in their domain of expertise</li>
                  <li><strong>Consistent Style:</strong> Responses follow consistent patterns and formats for your use case</li>
                </ul>
              </div>
              
              <div className="tutorial-section">
                <h4>Fine-Tuning vs. Prompting</h4>
                <p>
                  While clever prompting can improve model outputs, fine-tuning offers several advantages:
                </p>
                <table className="comparison-table">
                  <thead>
                    <tr>
                      <th>Prompting</th>
                      <th>Fine-Tuning</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Requires long, carefully crafted prompts</td>
                      <td>Works with shorter, simpler prompts</td>
                    </tr>
                    <tr>
                      <td>Uses token quota for each prompt</td>
                      <td>More token-efficient (smaller prompts)</td>
                    </tr>
                    <tr>
                      <td>Knowledge limited to pre-training data</td>
                      <td>Can learn new domain knowledge</td>
                    </tr>
                    <tr>
                      <td>Inconsistent adherence to instructions</td>
                      <td>Consistently follows learned patterns</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'lora' && (
            <div className="tutorial-content">
              <h3>LoRA & QLoRA Explained</h3>
              
              <div className="tutorial-section">
                <h4>What is LoRA?</h4>
                <p>
                  <strong>Low-Rank Adaptation (LoRA)</strong> is a technique that makes fine-tuning large language models much more efficient.
                </p>
                <p>
                  Instead of updating all parameters in a model (which can be billions), LoRA only trains small "adapter" matrices that are inserted into the model.
                </p>
                
                <div className="tutorial-image">
                  <img src="https://i.imgur.com/XYZ123.png" alt="LoRA diagram" />
                </div>
                
                <h4>Benefits of LoRA</h4>
                <ul>
                  <li><strong>Memory Efficient:</strong> Requires much less GPU memory than full fine-tuning</li>
                  <li><strong>Faster Training:</strong> Can be 3-5x faster than traditional fine-tuning</li>
                  <li><strong>Small Adapters:</strong> LoRA adapters are typically only a few MB in size (vs. GB for full models)</li>
                  <li><strong>Composable:</strong> Multiple LoRA adapters can be combined or swapped</li>
                </ul>
              </div>
              
              <div className="tutorial-section">
                <h4>What is QLoRA?</h4>
                <p>
                  <strong>Quantized Low-Rank Adaptation (QLoRA)</strong> combines LoRA with model quantization for even greater efficiency.
                </p>
                <p>
                  QLoRA reduces the precision of the base model (typically to 4-bit) while keeping the LoRA adapters in higher precision.
                </p>
                
                <h4>Benefits of QLoRA</h4>
                <ul>
                  <li><strong>Consumer Hardware:</strong> Fine-tune large models on consumer GPUs (even 8GB VRAM)</li>
                  <li><strong>Minimal Quality Loss:</strong> Performance nearly identical to full-precision fine-tuning</li>
                  <li><strong>Faster Training:</strong> Lower precision means faster computation</li>
                </ul>
                
                <div className="technical-note">
                  <h4>Technical Note</h4>
                  <p>
                    QLoRA uses a technique called <strong>Double Quantization</strong> to reduce the memory footprint of quantization constants, and <strong>Paged Optimizers</strong> to manage memory more efficiently during training.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'parameters' && (
            <div className="tutorial-content">
              <h3>Fine-Tuning Parameters Explained</h3>
              
              <div className="tutorial-section">
                <h4>Base Model Selection</h4>
                <p>
                  The pre-trained model that serves as the starting point for fine-tuning.
                </p>
                <ul>
                  <li><strong>Mistral 7B:</strong> Excellent performance-to-size ratio, good for most use cases</li>
                  <li><strong>Llama 2 7B:</strong> Well-documented model with strong reasoning capabilities</li>
                  <li><strong>StableLM 3B:</strong> Smaller model, faster but less capable</li>
                </ul>
                <p>
                  <strong>Recommendation:</strong> Start with Mistral 7B for most use cases.
                </p>
              </div>
              
              <div className="tutorial-section">
                <h4>LoRA Parameters</h4>
                
                <div className="parameter-explanation">
                  <h5>LoRA Rank</h5>
                  <p>
                    Controls the size and capacity of the LoRA adapters. Higher rank means more capacity but larger adapters.
                  </p>
                  <ul>
                    <li><strong>8-16:</strong> For simple adaptations or small datasets</li>
                    <li><strong>32-64:</strong> Good balance for most use cases</li>
                    <li><strong>128:</strong> For complex adaptations or large datasets</li>
                  </ul>
                </div>
                
                <div className="parameter-explanation">
                  <h5>LoRA Alpha</h5>
                  <p>
                    Scaling factor that affects how much influence the LoRA adapters have. Usually set to 2x the rank.
                  </p>
                </div>
                
                <div className="parameter-explanation">
                  <h5>LoRA Dropout</h5>
                  <p>
                    Regularization technique to prevent overfitting. Higher values (0.1-0.2) help with smaller datasets.
                  </p>
                </div>
              </div>
              
              <div className="tutorial-section">
                <h4>Training Parameters</h4>
                
                <div className="parameter-explanation">
                  <h5>Learning Rate</h5>
                  <p>
                    Controls how quickly the model adapts to the new data. Too high can cause instability, too low can result in slow or incomplete learning.
                  </p>
                  <ul>
                    <li><strong>1e-5 to 3e-5:</strong> Conservative, stable learning</li>
                    <li><strong>1e-4 to 3e-4:</strong> Balanced for most cases</li>
                  </ul>
                </div>
                
                <div className="parameter-explanation">
                  <h5>Batch Size</h5>
                  <p>
                    Number of samples processed together. Larger batches give more stable gradients but require more memory.
                  </p>
                  <p>
                    <strong>Recommendation:</strong> Use the largest batch size your GPU can handle.
                  </p>
                </div>
                
                <div className="parameter-explanation">
                  <h5>Number of Epochs</h5>
                  <p>
                    How many times the model will see the entire dataset during training.
                  </p>
                  <ul>
                    <li><strong>1-2 epochs:</strong> For very large datasets</li>
                    <li><strong>3-5 epochs:</strong> Good balance for most datasets</li>
                  </ul>
                </div>
              </div>
              
              <div className="tutorial-section">
                <h4>Quantization</h4>
                <p>
                  Reduces the precision of model weights to save memory.
                </p>
                <ul>
                  <li><strong>None:</strong> Full precision (FP16/BF16), highest quality but most memory intensive</li>
                  <li><strong>8-bit:</strong> Good balance of quality and memory savings</li>
                  <li><strong>4-bit:</strong> Maximum memory savings, slight quality degradation</li>
                </ul>
                <p>
                  <strong>Recommendation:</strong> Use 4-bit for most cases, especially on consumer hardware.
                </p>
              </div>
            </div>
          )}

          {activeTab === 'workflow' && (
            <div className="tutorial-content">
              <h3>Fine-Tuning Workflow</h3>
              
              <div className="tutorial-section">
                <h4>Step 1: Select Your Domain</h4>
                <p>
                  Choose the domain that best matches your use case (Fintech, Healthcare, Education, or Law).
                </p>
                <p>
                  This determines which datasets are available for fine-tuning.
                </p>
              </div>
              
              <div className="tutorial-section">
                <h4>Step 2: Choose a Dataset</h4>
                <p>
                  Select a dataset within your domain that aligns with your specific needs.
                </p>
                <p>
                  Each dataset contains examples that teach the model how to respond to queries in your domain.
                </p>
              </div>
              
              <div className="tutorial-section">
                <h4>Step 3: Configure Parameters</h4>
                <p>
                  Adjust the fine-tuning parameters based on your needs:
                </p>
                <ul>
                  <li>For most users, the default parameters work well</li>
                  <li>For advanced users, customize parameters based on your specific requirements</li>
                </ul>
              </div>
              
              <div className="tutorial-section">
                <h4>Step 4: Start Fine-Tuning</h4>
                <p>
                  Launch the fine-tuning job and monitor its progress.
                </p>
                <p>
                  Fine-tuning typically takes 30+ minutes depending on the dataset size and parameters.
                </p>
              </div>
              
              <div className="tutorial-section">
                <h4>Step 5: Use Your Fine-Tuned Model</h4>
                <p>
                  Once fine-tuning is complete, your model will appear in the "Fine-Tuned Models" tab.
                </p>
                <p>
                  You can then select it for use in the chat interface.
                </p>
              </div>
              
              <div className="tutorial-section">
                <h4>Best Practices</h4>
                <ul>
                  <li><strong>Start Simple:</strong> Use default parameters for your first fine-tuning job</li>
                  <li><strong>Iterative Approach:</strong> Test your model and refine with additional fine-tuning if needed</li>
                  <li><strong>Compare Models:</strong> Fine-tune multiple models with different parameters to find the best one</li>
                  <li><strong>Save Resources:</strong> Use 4-bit quantization to minimize memory usage</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button className="modal-button primary" onClick={handleClose}>Close</button>
        </div>
      </div>
    </div>
  );
};

export default FineTuningTutorial;
