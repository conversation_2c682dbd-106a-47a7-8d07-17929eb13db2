{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "echo \"🚀 Starting VertoAI Frontend...\" && vite", "start": "echo \"🚀 Starting VertoAI Frontend...\" && vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"autoprefixer": "^10.4.21", "axios": "^1.9.0", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^4.1.4", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}