import React, { useState } from 'react';

const TutorialMode = () => {
  const [tutorialEnabled, setTutorialEnabled] = useState(false);

  const toggleTutorial = () => {
    setTutorialEnabled(!tutorialEnabled);
  };

  return (
    <div className="control-section">
      <h2>
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="M12 16v-4"></path>
          <path d="M12 8h.01"></path>
        </svg>
        Tutorial Mode
      </h2>
      <button
        className={`tutorial-toggle ${tutorialEnabled ? 'active' : ''}`}
        onClick={toggleTutorial}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M18 6L6 18"></path>
          <path d="M6 6l12 12"></path>
        </svg>
        {tutorialEnabled ? 'Disable' : 'Enable'} Tutorial Mode
      </button>
      <p>Get helpful explanations about LoRA, fine-tuning, and more.</p>

      {tutorialEnabled && (
        <div className="tutorial-content">
          <div className="tutorial-item">
            <h3>What is LoRA?</h3>
            <p>Low-Rank Adaptation (LoRA) is a technique that efficiently fine-tunes large language models by training only a small set of parameters instead of the entire model.</p>
          </div>

          <div className="tutorial-item">
            <h3>What is QLoRA?</h3>
            <p>Quantized Low-Rank Adaptation (QLoRA) combines quantization (reducing model precision to 4-bit) with LoRA for even more efficient fine-tuning.</p>
          </div>

          <div className="tutorial-item">
            <h3>What are Adapters?</h3>
            <p>Adapters are small, trainable modules that are inserted between layers of a pre-trained model. They allow for efficient fine-tuning without modifying the original model weights.</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorialMode;
