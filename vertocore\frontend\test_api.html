<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VertoAI API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #4a6cf7;
        }
        button {
            background-color: #4a6cf7;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background-color: #3a5ce5;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        .response {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        .loading {
            display: none;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #4a6cf7;
            animation: spin 1s linear infinite;
            display: inline-block;
            vertical-align: middle;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>VertoAI API Test</h1>
    <p>This page tests the VertoAI backend API with OpenRouter integration.</p>
    
    <div>
        <label for="prompt">Enter your prompt:</label>
        <textarea id="prompt" placeholder="What is the capital of France?"></textarea>
    </div>
    
    <div>
        <label for="domain">Select domain:</label>
        <select id="domain">
            <option value="fintech">Finance & Banking</option>
            <option value="healthcare">Healthcare</option>
            <option value="education" selected>Education</option>
            <option value="law">Legal</option>
        </select>
    </div>
    
    <div>
        <label for="provider">Select provider:</label>
        <select id="provider">
            <option value="openrouter" selected>OpenRouter (google/gemma-3-12b-it:free)</option>
            <option value="openai">OpenAI</option>
            <option value="gemini">Google Gemini</option>
            <option value="claude">Anthropic Claude</option>
            <option value="deepseek">DeepSeek</option>
        </select>
    </div>
    
    <button id="submit">Submit</button>
    
    <div class="loading" id="loading">
        <div class="spinner"></div> Processing your request...
    </div>
    
    <div class="response" id="response"></div>
    
    <script>
        document.getElementById('submit').addEventListener('click', async () => {
            const prompt = document.getElementById('prompt').value;
            const domain = document.getElementById('domain').value;
            const provider = document.getElementById('provider').value;
            const responseElement = document.getElementById('response');
            const loadingElement = document.getElementById('loading');
            
            if (!prompt) {
                responseElement.textContent = 'Please enter a prompt.';
                return;
            }
            
            // Show loading indicator
            loadingElement.style.display = 'block';
            responseElement.textContent = '';
            
            try {
                const response = await fetch('http://localhost:5000/api/inference/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt,
                        provider,
                        domain,
                        api_key: '',  // Empty string for free tier
                        max_tokens: 500,
                        temperature: 0.7
                    }),
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Format and display the response
                const formattedResponse = `Response from ${data.provider} (${data.model}):\n\n${data.text}\n\nTokens used: ${JSON.stringify(data.tokens_used)}`;
                responseElement.textContent = formattedResponse;
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            } finally {
                // Hide loading indicator
                loadingElement.style.display = 'none';
            }
        });
    </script>
</body>
</html>
