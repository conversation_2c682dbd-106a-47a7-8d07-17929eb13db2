# VertoAI Frontend

This is the frontend for the VertoAI platform, built with React, Tailwind CSS, and Three.js.

## Features

- Interactive UI with 3D visualizations
- Domain selection (fintech, healthcare, education, law)
- LLM provider selection (OpenAI, Gemini, Claude, OpenRouter, DeepSeek)
- API key management
- Chat interface with history panel
- Tutorial mode for non-technical users

## Tech Stack

- React with Vite for fast development
- Tailwind CSS for styling
- Three.js for 3D visualizations
- Zustand for state management

## Development

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Build for production:
```bash
npm run build
```

## Project Structure

- `src/components/` - Reusable UI components
- `src/pages/` - Domain-specific templates
- `src/hooks/` - Custom React hooks
- `src/lib/` - Utility functions
- `src/store/` - Zustand state management
- `src/App.jsx` - Main app entry point
