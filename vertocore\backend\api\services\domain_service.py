"""
Domain service
Handles domain-specific logic
"""

# Available domains with metadata
DOMAINS = {
    'fintech': {
        'id': 'fintech',
        'name': 'Financial Technology',
        'description': 'Financial regulations, risk assessment, and investment analysis',
        'datasets': ['financial_regulations', 'investment_analysis', 'risk_assessment'],
        'examples': [
            'Analyze the risk profile of this investment portfolio',
            'Summarize the key points of SEC regulation 10b-5',
            'Generate a financial report based on these quarterly results'
        ]
    },
    'healthcare': {
        'id': 'healthcare',
        'name': 'Healthcare',
        'description': 'Medical diagnostics, treatment recommendations, and health records analysis',
        'datasets': ['medical_diagnostics', 'treatment_guidelines', 'health_records'],
        'examples': [
            'Suggest potential diagnoses based on these symptoms',
            'Summarize the latest research on diabetes treatments',
            'Extract key information from this patient history'
        ]
    },
    'education': {
        'id': 'education',
        'name': 'Education',
        'description': 'Curriculum development, student assessment, and educational content creation',
        'datasets': ['curriculum_standards', 'assessment_methods', 'educational_content'],
        'examples': [
            'Create a lesson plan for teaching photosynthesis to 5th graders',
            'Develop assessment questions for a high school algebra course',
            'Summarize this scientific paper for undergraduate students'
        ]
    },
    'law': {
        'id': 'law',
        'name': 'Legal',
        'description': 'Legal research, contract analysis, and case summarization',
        'datasets': ['legal_precedents', 'contract_clauses', 'case_law'],
        'examples': [
            'Summarize the key holdings in this court case',
            'Identify potential issues in this contract clause',
            'Research relevant precedents for this legal question'
        ]
    }
}

def get_domains():
    """
    Get list of available domains
    
    Returns:
        List of domain objects with basic info
    """
    return [
        {
            'id': domain_id,
            'name': domain_info['name'],
            'description': domain_info['description']
        }
        for domain_id, domain_info in DOMAINS.items()
    ]

def get_domain_info(domain_id):
    """
    Get detailed information about a specific domain
    
    Args:
        domain_id: Domain identifier
        
    Returns:
        Domain object with detailed info or None if not found
    """
    return DOMAINS.get(domain_id)
