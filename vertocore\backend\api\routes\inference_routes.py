"""
Inference routes for LLM providers
"""

from flask import jsonify, request
from . import inference_bp
from ..services.inference_service import run_inference, get_providers, generate_title_with_separate_key
from ..utils.logger import api_request, api_response

@inference_bp.route('/providers', methods=['GET'])
def list_providers():
    """
    Get list of available LLM providers

    Returns:
        JSON list of providers
    """
    api_request('GET', '/api/inference/providers')
    providers = get_providers()
    api_response(200, '/api/inference/providers')
    return jsonify(providers)

@inference_bp.route('/generate', methods=['POST'])
def generate_text():
    """
    Generate text using the selected LLM provider

    Request JSON:
        {
            "prompt": "Your prompt text",
            "provider": "openai",
            "domain": "fintech",
            "api_key": "your_api_key",
            "max_tokens": 500,
            "temperature": 0.7
        }

    Returns:
        JSON object with generated text
    """
    api_request('POST', '/api/inference/generate')
    data = request.json

    # Validate request
    if not data:
        api_response(400, '/api/inference/generate')
        return jsonify({"error": "No data provided"}), 400

    if 'prompt' not in data:
        api_response(400, '/api/inference/generate')
        return jsonify({"error": "No prompt provided"}), 400

    if 'provider' not in data:
        api_response(400, '/api/inference/generate')
        return jsonify({"error": "No provider specified"}), 400

    if 'api_key' not in data:
        api_response(400, '/api/inference/generate')
        return jsonify({"error": "No API key provided"}), 400

    # Extract parameters with defaults
    prompt = data['prompt']
    provider = data['provider']
    domain = data.get('domain', 'general')
    api_key = data['api_key']
    max_tokens = data.get('max_tokens', 500)
    temperature = data.get('temperature', 0.7)

    # Extract request ID and chat ID for tracing and context isolation
    request_id = data.get('request_id', None)
    chat_id = data.get('chat_id', None)

    # Also check headers for request ID and chat ID
    if not request_id and 'X-Request-ID' in request.headers:
        request_id = request.headers.get('X-Request-ID')

    if not chat_id and 'X-Chat-ID' in request.headers:
        chat_id = request.headers.get('X-Chat-ID')

    try:
        result = run_inference(
            prompt=prompt,
            provider=provider,
            domain=domain,
            api_key=api_key,
            max_tokens=max_tokens,
            temperature=temperature,
            request_id=request_id,
            chat_id=chat_id
        )

        api_response(200, '/api/inference/generate')
        return jsonify(result)
    except Exception as e:
        api_response(500, '/api/inference/generate')
        return jsonify({"error": str(e)}), 500

@inference_bp.route('/generate-title', methods=['POST'])
def generate_title():
    """
    Generate a title for a conversation using a separate API key

    Request JSON:
        {
            "messages": [{"sender": "user", "text": "message text"}, ...],
            "provider": "openrouter",
            "domain": "general"
        }

    Returns:
        JSON object with generated title
    """
    api_request('POST', '/api/inference/generate-title')
    data = request.json

    # Validate request
    if not data:
        api_response(400, '/api/inference/generate-title')
        return jsonify({"error": "No data provided"}), 400

    if 'messages' not in data:
        api_response(400, '/api/inference/generate-title')
        return jsonify({"error": "No messages provided"}), 400

    if 'provider' not in data:
        api_response(400, '/api/inference/generate-title')
        return jsonify({"error": "No provider specified"}), 400

    # Extract parameters with defaults
    messages = data['messages']
    provider = data['provider']
    domain = data.get('domain', 'general')

    try:
        result = generate_title_with_separate_key(
            messages=messages,
            provider=provider,
            domain=domain
        )

        api_response(200, '/api/inference/generate-title')
        return jsonify({"title": result})
    except Exception as e:
        api_response(500, '/api/inference/generate-title')
        return jsonify({"error": str(e)}), 500
