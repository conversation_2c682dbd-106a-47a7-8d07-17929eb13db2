# VertoAI Documentation

This directory contains documentation for the VertoAI platform.

## Contents

- `tutorial.md` - Non-technical user guide for using VertoAI
- `architecture-diagram.png` - System flowchart showing the architecture of VertoAI

## User Guides

### For Non-Technical Users

The `tutorial.md` file provides a step-by-step guide for non-technical users to:
- Select a domain (fintech, healthcare, education, law)
- Choose an LLM provider
- Enter an API key (optional)
- Use the chat interface
- Understand the results

### For Developers

Developers should refer to the README.md files in the `frontend` and `backend` directories for:
- Project structure
- Development setup
- API documentation
- Contribution guidelines

## Architecture

The VertoAI platform consists of:

1. **Frontend**: React + Tailwind + Three.js
   - User interface for interacting with the platform
   - 3D visualizations for model architecture
   - Domain-specific templates

2. **Backend**: Flask + LangChain + LangGraph
   - API for serving the frontend
   - Fine-tuning pipeline using PEFT
   - Inference routing to multiple LLM providers
   - Complex reasoning workflows

3. **Model Layer**: LoRA/QLoRA
   - Efficient fine-tuning of large language models
   - Quantization for reduced memory footprint
   - Adapter storage as .safetensors

4. **Security & Cost Control**
   - API key management
   - Rate limiting
   - Token usage tracking
