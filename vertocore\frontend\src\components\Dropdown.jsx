import React, { useState, useRef, useEffect } from 'react';

const Dropdown = ({
  options,
  selectedValue,
  onSelect,
  placeholder = 'Select an option'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Find the selected option
  const selectedOption = options.find(option => option.id === selectedValue);

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Handle option selection
  const handleSelect = (option) => {
    onSelect(option.id);
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`dropdown-container ${isOpen ? 'dropdown-open' : ''}`} ref={dropdownRef}>
      <div className="dropdown-header" onClick={toggleDropdown}>
        <span className="dropdown-selected">
          {selectedOption ? (
            <>
              {selectedOption.color && (
                <span
                  className="dropdown-color-indicator"
                  style={{ backgroundColor: selectedOption.color }}
                ></span>
              )}
              {selectedOption.label}
            </>
          ) : placeholder}
        </span>
        <span className="dropdown-icon">▼</span>
      </div>

      <div className="dropdown-menu">
        {options.map(option => (
          <div
            key={option.id}
            className={`dropdown-item ${option.id === selectedValue ? 'active' : ''}`}
            onClick={() => handleSelect(option)}
          >
            {option.color && (
              <span
                className="dropdown-color-indicator"
                style={{ backgroundColor: option.color }}
              ></span>
            )}
            {option.label}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Dropdown;
