"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PROVIDERS = exports.DOMAINS = exports.USE_SEPARATE_TITLE_KEY = exports.TESTING_MODE = exports.FREE_USAGE_LIMIT = exports.DEFAULT_MODEL = exports.API_URL = void 0;

/**
 * Configuration utility for VertoAI
 * Centralizes access to environment variables and configuration
 */
// API configuration
var API_URL = window.REACT_APP_API_URL || 'http://localhost:5000'; // Default model configuration

exports.API_URL = API_URL;
var DEFAULT_MODEL = 'google/gemma-3-12b-it:free'; // Free usage limits

exports.DEFAULT_MODEL = DEFAULT_MODEL;
var FREE_USAGE_LIMIT = 10; // Testing mode - set to true to disable usage limits

exports.FREE_USAGE_LIMIT = FREE_USAGE_LIMIT;
var TESTING_MODE = true; // Separate API key for title generation (to avoid using main quota)

exports.TESTING_MODE = TESTING_MODE;
var USE_SEPARATE_TITLE_KEY = true; // Default domains

exports.USE_SEPARATE_TITLE_KEY = USE_SEPARATE_TITLE_KEY;
var DOMAINS = [{
  id: 'fintech',
  name: 'Finance & Banking',
  description: 'Financial services, banking, investments, and regulations'
}, {
  id: 'healthcare',
  name: 'Healthcare',
  description: 'Medical information, healthcare systems, and patient care'
}, {
  id: 'education',
  name: 'Education',
  description: 'Learning, teaching, educational systems, and research'
}, {
  id: 'law',
  name: 'Legal',
  description: 'Legal information, regulations, and compliance'
}]; // Default providers

exports.DOMAINS = DOMAINS;
var PROVIDERS = [{
  id: 'openrouter',
  name: 'OpenRouter',
  models: ['google/gemma-3-12b-it:free'],
  requiresApiKey: false,
  // False because we have a shared API key for free tier
  defaultForFree: true,
  description: 'Free tier: 10 prompts/day shared limit. Add your own API key for 50+ prompts/day.'
}, {
  id: 'openai',
  name: 'OpenAI',
  models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
  requiresApiKey: true
}, {
  id: 'gemini',
  name: 'Google Gemini',
  models: ['gemini-pro', 'gemini-ultra'],
  requiresApiKey: true
}, {
  id: 'claude',
  name: 'Anthropic Claude',
  models: ['claude-instant-1', 'claude-2'],
  requiresApiKey: true
}, {
  id: 'deepseek',
  name: 'DeepSeek',
  models: ['deepseek-coder', 'deepseek-llm'],
  requiresApiKey: true
}];
exports.PROVIDERS = PROVIDERS;