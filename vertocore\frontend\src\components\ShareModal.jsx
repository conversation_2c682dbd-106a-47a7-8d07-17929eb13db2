import React, { useRef, useEffect, useState } from 'react';

const ShareModal = ({ isOpen, onClose, chatText, messages }) => {
  const [isClosing, setIsClosing] = useState(false);
  const [copied, setCopied] = useState(false);
  const [activeTab, setActiveTab] = useState('text');
  const modalRef = useRef(null);
  const textareaRef = useRef(null);

  // Handle modal close with animation
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      setIsClosing(false);
    }, 300); // Match this with CSS transition duration
  };

  // <PERSON>le click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Reset copied state when modal opens
  useEffect(() => {
    if (isOpen) {
      setCopied(false);
      setActiveTab('text');
    }
  }, [isOpen]);

  const handleCopy = () => {
    if (textareaRef.current) {
      textareaRef.current.select();
      document.execCommand('copy');
      setCopied(true);

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    }
  };

  const handleExportJson = () => {
    // Create a JSON object from the messages
    const jsonData = JSON.stringify(messages, null, 2);

    // Create a blob from the JSON data
    const blob = new Blob([jsonData], { type: 'application/json' });

    // Create a URL for the blob
    const url = URL.createObjectURL(blob);

    // Create a temporary anchor element
    const a = document.createElement('a');
    a.href = url;
    a.download = `vertoai-chat-${new Date().toISOString().slice(0, 10)}.json`;

    // Trigger a click on the anchor element
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" style={{ zIndex: 10000 }}>
      <div
        className={`modal-container share-modal ${isClosing ? 'modal-closing' : 'modal-opening'}`}
        ref={modalRef}
        style={{ position: 'relative', zIndex: 10001 }}
      >
        <div className="modal-header">
          <h2>Share Chat</h2>
          <button className="modal-close" onClick={handleClose}>×</button>
        </div>

        <div className="modal-tabs">
          <button
            className={`modal-tab ${activeTab === 'text' ? 'active' : ''}`}
            onClick={() => setActiveTab('text')}
          >
            Text
          </button>
          <button
            className={`modal-tab ${activeTab === 'json' ? 'active' : ''}`}
            onClick={() => setActiveTab('json')}
          >
            JSON
          </button>
        </div>

        <div className="modal-body">
          {activeTab === 'text' ? (
            <>
              <p className="share-description">Copy the text below to share this conversation:</p>

              <textarea
                ref={textareaRef}
                className="share-textarea"
                value={chatText}
                readOnly
              />

              <div className="share-options">
                <button
                  className="modal-button primary share-copy-button"
                  onClick={handleCopy}
                >
                  {copied ? (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polyline points="20 6 9 17 4 12"></polyline>
                      </svg>
                      Copied!
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                      </svg>
                      Copy to Clipboard
                    </>
                  )}
                </button>
              </div>
            </>
          ) : (
            <>
              <p className="share-description">Export this conversation as a JSON file:</p>

              <div className="json-preview">
                <pre>{JSON.stringify(messages, null, 2).slice(0, 300)}...</pre>
              </div>

              <div className="share-options">
                <button
                  className="modal-button primary share-download-button"
                  onClick={handleExportJson}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                  </svg>
                  Download JSON
                </button>
              </div>
            </>
          )}
        </div>

        <div className="modal-footer">
          <button className="modal-button secondary" onClick={handleClose}>Close</button>
        </div>
      </div>
    </div>
  );
};

export default ShareModal;
