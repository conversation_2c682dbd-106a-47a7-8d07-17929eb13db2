"""
Domain selection routes
"""

from flask import jsonify, request
from . import domain_bp
from ..services.domain_service import get_domains, get_domain_info

@domain_bp.route('/', methods=['GET'])
def list_domains():
    """
    Get list of available domains
    
    Returns:
        JSON list of domains
    """
    domains = get_domains()
    return jsonify(domains)

@domain_bp.route('/<domain_id>', methods=['GET'])
def get_domain(domain_id):
    """
    Get information about a specific domain
    
    Args:
        domain_id: Domain identifier (fintech, healthcare, etc.)
        
    Returns:
        JSON object with domain information
    """
    domain_info = get_domain_info(domain_id)
    
    if not domain_info:
        return jsonify({"error": f"Domain '{domain_id}' not found"}), 404
        
    return jsonify(domain_info)
