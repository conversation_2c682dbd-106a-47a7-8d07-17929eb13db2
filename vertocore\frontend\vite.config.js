import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// Simple plugin to ensure server starts and logs URL
const ensureServerStart = () => {
  return {
    name: 'ensure-server-start',
    configureServer(server) {
      server.httpServer?.once('listening', () => {
        console.log('\n  \x1b[1m\x1b[36mVertoAI Frontend\x1b[0m is running at: \x1b[1m\x1b[32mhttp://localhost:5173/\x1b[0m\n')
      })
    }
  }
}

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    ensureServerStart()
  ],

  // Set log level to info to ensure server address is printed
  logLevel: 'info',

  // Configure server
  server: {
    port: 5173,
    open: false, // Don't open browser automatically
    host: '0.0.0.0', // Listen on all addresses
  },
})
