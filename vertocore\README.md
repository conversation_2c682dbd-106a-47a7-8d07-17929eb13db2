# VertoAI

VertoAI is an open-source, multi-domain Gen AI platform that allows users to fine-tune LLMs for specific use cases (fintech, healthcare) and route inference to multiple providers (OpenAI, Gemini, Claude, DeepSeek) via OpenRouter. The platform prioritizes efficiency (LoRA/QLoRA), usability (non-dev tutorial mode), and extensibility (LangChain/LangGraph workflows).

## Features

- **Multi-domain Support**: Specialized for fintech, healthcare, education, and law domains
- **Efficient Fine-tuning**: LoRA/QLoRA for fast adaptation with minimal resources
- **Multiple LLM Providers**: Support for OpenAI, Google Gemini, Anthropic Claude, OpenRouter, and DeepSeek
- **Interactive UI**: React + Tailwind + Three.js for an intuitive, responsive interface with 3D visualizations
- **Advanced Workflows**: LangChain/LangGraph for complex reasoning paths
- **Non-technical User Support**: Tutorial mode with styled tooltips/explanations of technical concepts
- **RAG Integration**: LlamaIndex for retrieving domain-specific knowledge

## Tech Stack

### Frontend
- React with Vite
- Tailwind CSS for styling
- Three.js for 3D visualizations
- Zustand for state management
- Mobile support via React Native wrapper (planned)

### Backend
- Flask API
- LangChain/LangGraph for workflows
- PEFT library for LoRA/QLoRA fine-tuning
- Multiple LLM provider integrations via OpenRouter
- Rate-limiting and fallback logic

### Model Layer
- LoRA: Train low-rank matrices (rank=64) for fast adaptation
- QLoRA: Quantize base models to 4-bit (via bitsandbytes)
- AdamW optimizer with gradient checkpointing
- Store adapters as .safetensors (small size, easy versioning)

### Security & Cost Control
- API keys never stored; passed directly from frontend to backend
- Rate limiting via Flask middleware (500 req/day per user)
- Token usage tracking per request (displayed in UI)

## Project Structure

```
vertocore/
├── frontend/                   # React + Tailwind + Three.js UI
│   ├── public/                 # Static assets (logos, 3D models)
│   ├── src/
│   │   ├── components/         # Reusable UI components (dropdowns, modals)
│   │   ├── pages/              # Domain-specific templates (fintech/healthcare)
│   │   ├── hooks/              # Custom React hooks (e.g., API key validation)
│   │   ├── lib/                # Utility functions (theme toggler, animations)
│   │   ├── store/              # Zustand state management
│   │   └── App.jsx             # Main app entry point
│   └── vite.config.js          # Vite setup for hot-reloading
│
├── backend/                    # Flask API + LangChain/LangGraph
│   ├── api/
│   │   ├── routes/             # Endpoints for domain selection, inference
│   │   ├── services/           # Business logic (fine-tuning, routing)
│   │   └── __init__.py         # Flask app initialization
│   ├── models/                 # LoRA/QLoRA adapters and configs
│   ├── config.py               # OpenRouter/API keys
│   └── requirements.txt        # Python dependencies
│
├── docs/                       # Tutorials, API docs, and design specs
│   ├── tutorial.md             # Non-dev guide
│   └── architecture-diagram.png# System flowchart
│
├── .env                        # Environment variables
├── Dockerfile                  # Containerization (optional)
└── README.md                   # Project overview
```

## Getting Started

### Prerequisites
- Node.js (v16+)
- Python (v3.9+)
- API keys for LLM providers (optional)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/vertoai.git
cd vertoai
```

2. Set up the frontend:
```bash
cd vertocore/frontend
npm install
npm run dev
```

3. Set up the backend:
```bash
cd vertocore/backend
pip install -r requirements.txt
python -m flask run
```

4. Copy the `.env.example` file to `.env` and fill in your API keys:
```bash
cp .env.example .env
```

## Usage

1. Open your browser and navigate to `http://localhost:5173`
2. Select a domain (fintech, healthcare, education, law)
3. Choose an LLM provider
4. Enter your API key (optional - free tier available)
5. Start using the platform for domain-specific AI tasks

## Fine-tuning

To fine-tune a model:

1. Select a domain and base model (e.g., mistralai/Mistral-7B)
2. Choose a dataset (e.g., financial regulations, medical data)
3. Set fine-tuning parameters (or use defaults)
   - LoRA rank: 64
   - LoRA alpha: 16
   - LoRA dropout: 0.1
   - Learning rate: 3e-4
   - Batch size: 8
   - Number of epochs: 3
4. Start the fine-tuning process
5. Wait for completion
6. Use your fine-tuned model

## Development Plan

1. Basic UI with dropdowns (React + Tailwind)
2. OpenRouter API integration (Flask + Axios)
3. QLoRA fine-tuning pipeline (PEFT + Transformers)
4. LangChain/LangGraph workflows
5. Mobile app (React Native)
6. Tutorial mode + 3D visuals (Three.js)

## Ethics & Future Extensions

- **Bias Mitigation**: Add fairness checks during dataset preprocessing
- **Monetization**: Free tier with ads; premium tier removes rate limits
- **Community**: Open-source under MIT license to encourage contributions

## Deployment Strategy

- **Frontend**: Deploy to Vercel/Cloudflare Pages (free tier)
- **Backend**: Host on Hugging Face Inference Endpoints or RunPod.io
- **Database**: Use Supabase for storing user preferences

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Hugging Face](https://huggingface.co/) for transformer models
- [LangChain](https://langchain.com/) for LLM application framework
- [PEFT](https://github.com/huggingface/peft) for efficient fine-tuning
- [OpenRouter](https://openrouter.ai/) for LLM API access
