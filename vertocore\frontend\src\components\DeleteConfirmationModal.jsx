import React, { useRef, useEffect, useState } from 'react';

const DeleteConfirmationModal = ({ isOpen, onClose, onConfirm, chatTitle }) => {
  const [isClosing, setIsClosing] = useState(false);
  const modalRef = useRef(null);

  // Handle modal close with animation
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      setIsClosing(false);
    }, 300); // Match this with CSS transition duration
  };

  // <PERSON>le click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Handle confirm action
  const handleConfirm = () => {
    onConfirm();
    handleClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div
        className="modal-container confirmation-modal danger-modal modal-opening"
        ref={modalRef}
      >
        <div className="modal-header">
          <h2>Delete Chat</h2>
          <button className="modal-close" onClick={handleClose}>×</button>
        </div>

        <div className="modal-body">
          <p className="confirmation-message">
            Are you sure you want to delete "{chatTitle}"? This action cannot be undone.
          </p>
        </div>

        <div className="modal-footer">
          <button className="modal-button secondary" onClick={handleClose}>Cancel</button>
          <button
            className="modal-button danger"
            onClick={handleConfirm}
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;
