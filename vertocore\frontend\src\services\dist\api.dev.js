"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.listFineTunedModels = exports.getFineTuningStatus = exports.startFineTuning = exports.getFineTuningParameters = exports.validateApiKey = exports.generateTitle = exports.getDomains = exports.getProviders = exports.generateText = void 0;

var _config = require("../utils/config");

function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance"); }

function _iterableToArrayLimit(arr, i) { if (!(Symbol.iterator in Object(arr) || Object.prototype.toString.call(arr) === "[object Arguments]")) { return; } var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

/**
 * Simple cache for API responses to prevent duplicate requests
 */
var apiCache = {
  cache: new Map(),
  // Generate a cache key from request parameters
  getKey: function getKey(prompt, provider, domain, apiKey, maxTokens, temperature) {
    // Extract chat ID from prompt if present to ensure each chat has its own cache entries
    var chatId = "default";

    if (prompt.startsWith('[Chat ID:')) {
      var endIndex = prompt.indexOf(']');

      if (endIndex > 0) {
        chatId = prompt.substring(9, endIndex).trim();
      }
    }

    return "".concat(chatId, "|").concat(prompt, "|").concat(provider, "|").concat(domain, "|").concat(!!apiKey, "|").concat(maxTokens, "|").concat(temperature);
  },
  // Get a cached response if it exists and is recent (less than 30 minutes old)
  get: function get(key) {
    if (apiCache.cache.has(key)) {
      var _apiCache$cache$get = apiCache.cache.get(key),
          timestamp = _apiCache$cache$get.timestamp,
          data = _apiCache$cache$get.data;

      var now = Date.now();
      var thirtyMinutes = 30 * 60 * 1000; // Increased from 5 to 30 minutes
      // Only use cache if it's less than 30 minutes old

      if (now - timestamp < thirtyMinutes) {
        // Don't log cache hits to reduce console clutter
        return data;
      }
    }

    return null;
  },
  // Store a response in the cache
  set: function set(key, data) {
    // Don't cache error responses
    if (data && data.error) {
      console.log('Not caching error response');
      return;
    }

    apiCache.cache.set(key, {
      timestamp: Date.now(),
      data: data
    }); // Limit cache size to 200 entries

    if (apiCache.cache.size > 200) {
      // Find the oldest entry
      var oldestKey = null;
      var oldestTime = Date.now();
      var _iteratorNormalCompletion = true;
      var _didIteratorError = false;
      var _iteratorError = undefined;

      try {
        for (var _iterator = apiCache.cache.entries()[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {
          var _step$value = _slicedToArray(_step.value, 2),
              k = _step$value[0],
              v = _step$value[1];

          if (v.timestamp < oldestTime) {
            oldestTime = v.timestamp;
            oldestKey = k;
          }
        }
      } catch (err) {
        _didIteratorError = true;
        _iteratorError = err;
      } finally {
        try {
          if (!_iteratorNormalCompletion && _iterator["return"] != null) {
            _iterator["return"]();
          }
        } finally {
          if (_didIteratorError) {
            throw _iteratorError;
          }
        }
      }

      if (oldestKey) {
        apiCache.cache["delete"](oldestKey);
      }
    }
  }
};
/**
 * Generate text using the selected LLM provider
 *
 * @param {string} prompt - User prompt text
 * @param {string} provider - LLM provider ID
 * @param {string} domain - Domain ID for context
 * @param {string} apiKey - Provider API key
 * @param {number} maxTokens - Maximum tokens to generate
 * @param {number} temperature - Sampling temperature
 * @param {string} requestId - Optional unique ID for this specific request
 * @param {string} chatId - Optional chat ID this request belongs to
 * @returns {Promise} - Promise with generated text response
 */

var generateText = function generateText(prompt, provider, domain, apiKey) {
  var maxTokens,
      temperature,
      requestId,
      chatId,
      isChatMessage,
      endIndex,
      cacheKey,
      cachedResponse,
      finalPrompt,
      response,
      errorData,
      result,
      _cacheKey,
      _args = arguments;

  return regeneratorRuntime.async(function generateText$(_context) {
    while (1) {
      switch (_context.prev = _context.next) {
        case 0:
          maxTokens = _args.length > 4 && _args[4] !== undefined ? _args[4] : 500;
          temperature = _args.length > 5 && _args[5] !== undefined ? _args[5] : 0.7;
          requestId = _args.length > 6 && _args[6] !== undefined ? _args[6] : null;
          chatId = _args.length > 7 && _args[7] !== undefined ? _args[7] : null;
          _context.prev = 4;
          // For chat messages, we completely disable caching to ensure each chat gets a fresh response
          // This prevents confusion between different chats
          isChatMessage = prompt.startsWith('[Chat ID:'); // Extract chat ID from prompt if not provided

          if (!chatId && isChatMessage) {
            endIndex = prompt.indexOf(']');

            if (endIndex > 0) {
              chatId = prompt.substring(9, endIndex).trim();
            }
          } // If chat ID is still not available, use a default


          if (!chatId) {
            chatId = "unknown";
          } // Generate a unique request ID if not provided


          if (!requestId) {
            requestId = Date.now().toString(36) + Math.random().toString(36).substring(2, 9);
          } // Only use cache for non-chat messages


          if (isChatMessage) {
            _context.next = 14;
            break;
          }

          // Check cache first to avoid duplicate requests
          cacheKey = apiCache.getKey(prompt, provider, domain, apiKey, maxTokens, temperature);
          cachedResponse = apiCache.get(cacheKey);

          if (!cachedResponse) {
            _context.next = 14;
            break;
          }

          return _context.abrupt("return", cachedResponse);

        case 14:
          // For OpenRouter, we can send an empty API key as the backend will use the environment variable
          // Make sure the request ID is included in the prompt
          finalPrompt = prompt;

          if (!prompt.includes("[Request ID: ".concat(requestId, "]"))) {
            // Add the request ID to the prompt if it's not already there
            finalPrompt = "".concat(prompt, "\n\n[Request ID: ").concat(requestId, "]");
          } // Make a POST request to the backend API


          _context.next = 18;
          return regeneratorRuntime.awrap(fetch("".concat(_config.API_URL, "/api/inference/generate"), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Request-ID': requestId,
              // Add request ID to headers for tracing
              'X-Chat-ID': chatId // Add chat ID to headers for tracing

            },
            body: JSON.stringify({
              prompt: finalPrompt,
              provider: provider,
              domain: domain,
              api_key: apiKey || '',
              // Send empty string if no API key provided
              max_tokens: maxTokens,
              temperature: temperature,
              request_id: requestId,
              // Include in body as well for backend processing
              chat_id: chatId // Include chat ID in body for backend processing

            })
          }));

        case 18:
          response = _context.sent;

          if (response.ok) {
            _context.next = 24;
            break;
          }

          _context.next = 22;
          return regeneratorRuntime.awrap(response.json());

        case 22:
          errorData = _context.sent;
          throw new Error(errorData.error || 'Failed to generate text');

        case 24:
          _context.next = 26;
          return regeneratorRuntime.awrap(response.json());

        case 26:
          result = _context.sent;
          // Add the request ID and chat ID to the result for tracking
          result.requestId = requestId;
          result.chatId = chatId; // Only cache non-chat messages

          if (!isChatMessage) {
            // Cache the successful response
            _cacheKey = apiCache.getKey(prompt, provider, domain, apiKey, maxTokens, temperature);
            apiCache.set(_cacheKey, result);
          }

          return _context.abrupt("return", result);

        case 33:
          _context.prev = 33;
          _context.t0 = _context["catch"](4);
          return _context.abrupt("return", {
            text: "Error: ".concat(_context.t0.message || 'Failed to generate text'),
            error: true,
            requestId: requestId,
            chatId: chatId
          });

        case 36:
        case "end":
          return _context.stop();
      }
    }
  }, null, null, [[4, 33]]);
};
/**
 * Get list of available LLM providers
 *
 * @returns {Promise} - Promise with list of providers
 */


exports.generateText = generateText;

var getProviders = function getProviders() {
  var response, errorData;
  return regeneratorRuntime.async(function getProviders$(_context2) {
    while (1) {
      switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          _context2.next = 3;
          return regeneratorRuntime.awrap(fetch("".concat(_config.API_URL, "/api/inference/providers")));

        case 3:
          response = _context2.sent;

          if (response.ok) {
            _context2.next = 9;
            break;
          }

          _context2.next = 7;
          return regeneratorRuntime.awrap(response.json());

        case 7:
          errorData = _context2.sent;
          throw new Error(errorData.error || 'Failed to fetch providers');

        case 9:
          _context2.next = 11;
          return regeneratorRuntime.awrap(response.json());

        case 11:
          return _context2.abrupt("return", _context2.sent);

        case 14:
          _context2.prev = 14;
          _context2.t0 = _context2["catch"](0);
          return _context2.abrupt("return", []);

        case 17:
        case "end":
          return _context2.stop();
      }
    }
  }, null, null, [[0, 14]]);
};
/**
 * Get list of available domains
 *
 * @returns {Promise} - Promise with list of domains
 */


exports.getProviders = getProviders;

var getDomains = function getDomains() {
  var response, errorData;
  return regeneratorRuntime.async(function getDomains$(_context3) {
    while (1) {
      switch (_context3.prev = _context3.next) {
        case 0:
          _context3.prev = 0;
          _context3.next = 3;
          return regeneratorRuntime.awrap(fetch("".concat(_config.API_URL, "/api/domain")));

        case 3:
          response = _context3.sent;

          if (response.ok) {
            _context3.next = 9;
            break;
          }

          _context3.next = 7;
          return regeneratorRuntime.awrap(response.json());

        case 7:
          errorData = _context3.sent;
          throw new Error(errorData.error || 'Failed to fetch domains');

        case 9:
          _context3.next = 11;
          return regeneratorRuntime.awrap(response.json());

        case 11:
          return _context3.abrupt("return", _context3.sent);

        case 14:
          _context3.prev = 14;
          _context3.t0 = _context3["catch"](0);
          return _context3.abrupt("return", []);

        case 17:
        case "end":
          return _context3.stop();
      }
    }
  }, null, null, [[0, 14]]);
};
/**
 * Simple cache for title generation to prevent duplicate requests
 */


exports.getDomains = getDomains;
var titleCache = new Map();
/**
 * Generate a title for a conversation based on messages
 *
 * @param {Array} messages - Array of conversation messages
 * @param {string} provider - LLM provider ID
 * @param {string} apiKey - Provider API key
 * @returns {Promise} - Promise with generated title
 */

var generateTitle = function generateTitle(messages, provider, apiKey) {
  var cacheKey, keys, i, response, data, userMessages, firstUserMessage, isGreeting, mainUserMessage, text, simpleTitle, firstSentence, words, capitalizedWords, keyTerms, importantWords, keyWords, shortTitle, _firstUserMessage, fallbackTitle, _firstUserMessage2, _fallbackTitle;

  return regeneratorRuntime.async(function generateTitle$(_context4) {
    while (1) {
      switch (_context4.prev = _context4.next) {
        case 0:
          _context4.prev = 0;
          // Create a simple cache key based on the first few messages
          cacheKey = messages.slice(0, 3).map(function (m) {
            return "".concat(m.sender, ":").concat(m.text.substring(0, 50));
          }).join('|'); // Check if we have a cached title

          if (!titleCache.has(cacheKey)) {
            _context4.next = 4;
            break;
          }

          return _context4.abrupt("return", titleCache.get(cacheKey));

        case 4:
          // If we have more than 50 titles cached, remove the oldest ones
          if (titleCache.size > 50) {
            // Get the keys sorted by age (assuming they were added in order)
            keys = Array.from(titleCache.keys()); // Remove the 10 oldest keys

            for (i = 0; i < 10 && i < keys.length; i++) {
              titleCache["delete"](keys[i]);
            }
          } // If USE_SEPARATE_TITLE_KEY is enabled, use the backend endpoint for title generation


          if (!(_config.USE_SEPARATE_TITLE_KEY && provider === 'openrouter')) {
            _context4.next = 22;
            break;
          }

          _context4.prev = 6;
          _context4.next = 9;
          return regeneratorRuntime.awrap(fetch("".concat(_config.API_URL, "/api/inference/generate-title"), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              messages: messages.slice(0, 3),
              // Only send first 3 messages
              provider: provider,
              domain: 'general'
            })
          }));

        case 9:
          response = _context4.sent;

          if (response.ok) {
            _context4.next = 12;
            break;
          }

          throw new Error("Title generation failed");

        case 12:
          _context4.next = 14;
          return regeneratorRuntime.awrap(response.json());

        case 14:
          data = _context4.sent;

          if (!data.title) {
            _context4.next = 18;
            break;
          }

          // Cache and return the generated title
          titleCache.set(cacheKey, data.title);
          return _context4.abrupt("return", data.title);

        case 18:
          _context4.next = 22;
          break;

        case 20:
          _context4.prev = 20;
          _context4.t0 = _context4["catch"](6);

        case 22:
          _context4.prev = 22;
          // Find all user messages
          userMessages = messages.filter(function (msg) {
            return msg.sender === 'user';
          });

          if (!(userMessages.length === 0)) {
            _context4.next = 26;
            break;
          }

          return _context4.abrupt("return", 'New Conversation');

        case 26:
          // Get the first substantive user message (not just a greeting)
          firstUserMessage = userMessages[0];
          isGreeting = firstUserMessage.text.trim().toLowerCase().match(/^(hi|hello|hey|greetings|yo)$/); // If first message is just a greeting and we have more messages, use the second one

          mainUserMessage = isGreeting && userMessages.length > 1 ? userMessages[1] : firstUserMessage;
          text = mainUserMessage.text.trim(); // If the message is short enough, just use it as the title

          if (!(text.length <= 40 && text.split(' ').length <= 8)) {
            _context4.next = 34;
            break;
          }

          simpleTitle = text;
          titleCache.set(cacheKey, simpleTitle);
          return _context4.abrupt("return", simpleTitle);

        case 34:
          // Try to extract a title from the first sentence if it's short
          firstSentence = text.split(/[.!?]/, 1)[0].trim();

          if (!(firstSentence.length <= 40 && firstSentence.split(' ').length <= 8)) {
            _context4.next = 38;
            break;
          }

          titleCache.set(cacheKey, firstSentence);
          return _context4.abrupt("return", firstSentence);

        case 38:
          // Try to identify key nouns and important words
          // This is a simple approach - look for capitalized words and longer words
          words = text.split(/\s+/); // First try to find capitalized words (proper nouns, concepts, etc.)

          capitalizedWords = words.filter(function (word) {
            return word.length > 1 && word[0] === word[0].toUpperCase() && word[0] !== word[0].toLowerCase() && !['I', 'A', 'An', 'The', 'In', 'On', 'At', 'For', 'With', 'By', 'To', 'And'].includes(word);
          });

          if (!(capitalizedWords.length >= 2 && capitalizedWords.length <= 5)) {
            _context4.next = 45;
            break;
          }

          keyTerms = capitalizedWords.join(' ');

          if (!(keyTerms.length <= 50)) {
            _context4.next = 45;
            break;
          }

          titleCache.set(cacheKey, keyTerms);
          return _context4.abrupt("return", keyTerms);

        case 45:
          // Next try to find important words (longer than 4 chars)
          importantWords = words.filter(function (word) {
            return word.length > 4;
          }).filter(function (word) {
            return !['about', 'after', 'again', 'below', 'could', 'every', 'first', 'found', 'great', 'house', 'large', 'learn', 'never', 'other', 'place', 'small', 'study', 'their', 'there', 'these', 'thing', 'think', 'three', 'water', 'where', 'which', 'world', 'would', 'write'].includes(word.toLowerCase());
          });

          if (!(importantWords.length >= 2)) {
            _context4.next = 51;
            break;
          }

          keyWords = importantWords.slice(0, 4).join(' ');

          if (!(keyWords.length <= 50)) {
            _context4.next = 51;
            break;
          }

          titleCache.set(cacheKey, keyWords);
          return _context4.abrupt("return", keyWords);

        case 51:
          // Last resort: Just use the first 30 chars of the message
          shortTitle = text.substring(0, 30) + (text.length > 30 ? '...' : '');
          titleCache.set(cacheKey, shortTitle);
          return _context4.abrupt("return", shortTitle);

        case 56:
          _context4.prev = 56;
          _context4.t1 = _context4["catch"](22);
          // If anything fails in our improved logic, fall back to simplest approach
          _firstUserMessage = messages.find(function (msg) {
            return msg.sender === 'user';
          });

          if (!_firstUserMessage) {
            _context4.next = 62;
            break;
          }

          fallbackTitle = _firstUserMessage.text.substring(0, 30) + (_firstUserMessage.text.length > 30 ? '...' : '');
          return _context4.abrupt("return", fallbackTitle);

        case 62:
          return _context4.abrupt("return", 'New Conversation');

        case 63:
          return _context4.abrupt("return", 'New Conversation');

        case 66:
          _context4.prev = 66;
          _context4.t2 = _context4["catch"](0);
          // Silently use fallback without logging error details
          // Fallback: Use the first user message as the title
          _firstUserMessage2 = messages.find(function (msg) {
            return msg.sender === 'user';
          });

          if (!_firstUserMessage2) {
            _context4.next = 72;
            break;
          }

          _fallbackTitle = _firstUserMessage2.text.substring(0, 30) + (_firstUserMessage2.text.length > 30 ? '...' : '');
          return _context4.abrupt("return", _fallbackTitle);

        case 72:
          return _context4.abrupt("return", 'New Conversation');

        case 73:
        case "end":
          return _context4.stop();
      }
    }
  }, null, null, [[0, 66], [6, 20], [22, 56]]);
};
/**
 * Check if an API key is valid
 *
 * @param {string} provider - LLM provider ID
 * @param {string} apiKey - Provider API key
 * @returns {Promise<boolean>} - Promise with validation result
 */


exports.generateTitle = generateTitle;

var validateApiKey = function validateApiKey(provider, apiKey) {
  var prompt, response;
  return regeneratorRuntime.async(function validateApiKey$(_context5) {
    while (1) {
      switch (_context5.prev = _context5.next) {
        case 0:
          if (!(provider === 'openrouter')) {
            _context5.next = 4;
            break;
          }

          if (!(apiKey && apiKey.startsWith('sk-') && apiKey.length > 20)) {
            _context5.next = 3;
            break;
          }

          return _context5.abrupt("return", true);

        case 3:
          return _context5.abrupt("return", false);

        case 4:
          _context5.prev = 4;
          // For other providers, use a minimal validation prompt
          prompt = "Test";
          _context5.next = 8;
          return regeneratorRuntime.awrap(generateText(prompt, provider, 'general', apiKey, 5, 0.5));

        case 8:
          response = _context5.sent;
          return _context5.abrupt("return", !!response.text);

        case 12:
          _context5.prev = 12;
          _context5.t0 = _context5["catch"](4);
          return _context5.abrupt("return", false);

        case 15:
        case "end":
          return _context5.stop();
      }
    }
  }, null, null, [[4, 12]]);
};
/**
 * Fine-tuning API functions
 */

/**
 * Get available fine-tuning parameters
 *
 * @returns {Promise} - Promise with fine-tuning parameters
 */


exports.validateApiKey = validateApiKey;

var getFineTuningParameters = function getFineTuningParameters() {
  var response;
  return regeneratorRuntime.async(function getFineTuningParameters$(_context6) {
    while (1) {
      switch (_context6.prev = _context6.next) {
        case 0:
          _context6.prev = 0;
          _context6.next = 3;
          return regeneratorRuntime.awrap(fetch("".concat(_config.API_URL, "/api/fine-tuning/parameters")));

        case 3:
          response = _context6.sent;

          if (response.ok) {
            _context6.next = 6;
            break;
          }

          throw new Error("Failed to fetch fine-tuning parameters: ".concat(response.status));

        case 6:
          _context6.next = 8;
          return regeneratorRuntime.awrap(response.json());

        case 8:
          return _context6.abrupt("return", _context6.sent);

        case 11:
          _context6.prev = 11;
          _context6.t0 = _context6["catch"](0);
          throw _context6.t0;

        case 14:
        case "end":
          return _context6.stop();
      }
    }
  }, null, null, [[0, 11]]);
};
/**
 * Start a fine-tuning job
 *
 * @param {Object} data - Fine-tuning job data
 * @returns {Promise} - Promise with job information
 */


exports.getFineTuningParameters = getFineTuningParameters;

var startFineTuning = function startFineTuning(data) {
  var response, errorData;
  return regeneratorRuntime.async(function startFineTuning$(_context7) {
    while (1) {
      switch (_context7.prev = _context7.next) {
        case 0:
          _context7.prev = 0;
          _context7.next = 3;
          return regeneratorRuntime.awrap(fetch("".concat(_config.API_URL, "/api/fine-tuning/start"), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          }));

        case 3:
          response = _context7.sent;

          if (response.ok) {
            _context7.next = 9;
            break;
          }

          _context7.next = 7;
          return regeneratorRuntime.awrap(response.json());

        case 7:
          errorData = _context7.sent;
          throw new Error(errorData.error || "Failed to start fine-tuning: ".concat(response.status));

        case 9:
          _context7.next = 11;
          return regeneratorRuntime.awrap(response.json());

        case 11:
          return _context7.abrupt("return", _context7.sent);

        case 14:
          _context7.prev = 14;
          _context7.t0 = _context7["catch"](0);
          throw _context7.t0;

        case 17:
        case "end":
          return _context7.stop();
      }
    }
  }, null, null, [[0, 14]]);
};
/**
 * Get status of a fine-tuning job
 *
 * @param {string} jobId - Fine-tuning job ID
 * @returns {Promise} - Promise with job status
 */


exports.startFineTuning = startFineTuning;

var getFineTuningStatus = function getFineTuningStatus(jobId) {
  var response, errorData;
  return regeneratorRuntime.async(function getFineTuningStatus$(_context8) {
    while (1) {
      switch (_context8.prev = _context8.next) {
        case 0:
          _context8.prev = 0;
          _context8.next = 3;
          return regeneratorRuntime.awrap(fetch("".concat(_config.API_URL, "/api/fine-tuning/status/").concat(jobId)));

        case 3:
          response = _context8.sent;

          if (response.ok) {
            _context8.next = 9;
            break;
          }

          _context8.next = 7;
          return regeneratorRuntime.awrap(response.json());

        case 7:
          errorData = _context8.sent;
          throw new Error(errorData.error || "Failed to get job status: ".concat(response.status));

        case 9:
          _context8.next = 11;
          return regeneratorRuntime.awrap(response.json());

        case 11:
          return _context8.abrupt("return", _context8.sent);

        case 14:
          _context8.prev = 14;
          _context8.t0 = _context8["catch"](0);
          throw _context8.t0;

        case 17:
        case "end":
          return _context8.stop();
      }
    }
  }, null, null, [[0, 14]]);
};
/**
 * List fine-tuned models
 *
 * @returns {Promise} - Promise with list of fine-tuned models
 */


exports.getFineTuningStatus = getFineTuningStatus;

var listFineTunedModels = function listFineTunedModels() {
  var response, errorData;
  return regeneratorRuntime.async(function listFineTunedModels$(_context9) {
    while (1) {
      switch (_context9.prev = _context9.next) {
        case 0:
          _context9.prev = 0;
          _context9.next = 3;
          return regeneratorRuntime.awrap(fetch("".concat(_config.API_URL, "/api/fine-tuning/models")));

        case 3:
          response = _context9.sent;

          if (response.ok) {
            _context9.next = 9;
            break;
          }

          _context9.next = 7;
          return regeneratorRuntime.awrap(response.json());

        case 7:
          errorData = _context9.sent;
          throw new Error(errorData.error || "Failed to list fine-tuned models: ".concat(response.status));

        case 9:
          _context9.next = 11;
          return regeneratorRuntime.awrap(response.json());

        case 11:
          return _context9.abrupt("return", _context9.sent);

        case 14:
          _context9.prev = 14;
          _context9.t0 = _context9["catch"](0);
          throw _context9.t0;

        case 17:
        case "end":
          return _context9.stop();
      }
    }
  }, null, null, [[0, 14]]);
};

exports.listFineTunedModels = listFineTunedModels;