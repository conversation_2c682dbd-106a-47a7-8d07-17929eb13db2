"""
Test script for OpenRouter API integration - direct test without Flask
"""

import os
import requests
from pathlib import Path
from dotenv import load_dotenv

# Get the project root directory
project_root = Path(__file__).parent.parent

# Load environment variables from the project root .env file
env_path = project_root / '.env'
load_dotenv(env_path)

print(f"Looking for .env file in: {env_path}")
print(f"Does .env file exist? {env_path.exists()}")

# Try to read the .env file directly
try:
    with open(env_path, 'r') as f:
        env_content = f.read()
        print(f"First 50 chars of .env file: {env_content[:50]}...")
except Exception as e:
    print(f"Error reading .env file: {e}")

# Get API key from environment
api_key = os.getenv('OPENROUTER_API_KEY')

print(f"API key found: {'Yes' if api_key else 'No'}")
if api_key:
    print(f"API key first 8 chars: {api_key[:8]}, last 4 chars: {api_key[-4:]}")

# Test OpenRouter API directly
def test_openrouter_api():
    if not api_key:
        print("No API key found, cannot test")
        return
        
    url = "https://openrouter.ai/api/v1/chat/completions"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
        "HTTP-Referer": "https://vertoai.com",
        "X-Title": "VertoAI Platform"
    }
    
    payload = {
        "model": "qwen/qwen3-32b:free",
        "messages": [
            {"role": "user", "content": "Say hello"}
        ],
        "max_tokens": 50,
        "temperature": 0.7
    }
    
    print("\nTesting OpenRouter API...")
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    print(f"Payload: {payload}")
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("Response successful!")
            print(f"Response data: {response_data}")
            
            if 'choices' in response_data and len(response_data['choices']) > 0:
                if 'message' in response_data['choices'][0]:
                    generated_text = response_data['choices'][0]['message']['content']
                    print(f"Generated text: {generated_text}")
        else:
            print(f"Error response: {response.text}")
    except Exception as e:
        print(f"Error testing API: {e}")

if __name__ == "__main__":
    test_openrouter_api()
