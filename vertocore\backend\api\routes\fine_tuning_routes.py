"""
Fine-tuning routes for LoRA/QLoRA
"""

from flask import jsonify, request
from . import fine_tuning_bp
from ..services.fine_tuning_service import (
    start_fine_tuning,
    get_fine_tuning_status,
    list_fine_tuned_models,
    get_fine_tuning_parameters
)

@fine_tuning_bp.route('/parameters', methods=['GET'])
def get_parameters():
    """
    Get available fine-tuning parameters
    
    Returns:
        JSON object with parameter options
    """
    parameters = get_fine_tuning_parameters()
    return jsonify(parameters)

@fine_tuning_bp.route('/start', methods=['POST'])
def start_training():
    """
    Start fine-tuning a model
    
    Request JSON:
        {
            "base_model": "mistralai/Mistral-7B-v0.1",
            "domain": "fintech",
            "dataset": "financial_regulations",
            "api_key": "your_api_key",
            "parameters": {
                "lora_rank": 64,
                "lora_alpha": 16,
                "lora_dropout": 0.1,
                "learning_rate": 3e-4,
                "batch_size": 8,
                "num_epochs": 3
            }
        }
    
    Returns:
        JSON object with job ID and status
    """
    data = request.json
    
    # Validate request
    if not data:
        return jsonify({"error": "No data provided"}), 400
        
    if 'base_model' not in data:
        return jsonify({"error": "No base model specified"}), 400
        
    if 'domain' not in data:
        return jsonify({"error": "No domain specified"}), 400
        
    if 'dataset' not in data:
        return jsonify({"error": "No dataset specified"}), 400
        
    if 'api_key' not in data:
        return jsonify({"error": "No API key provided"}), 400
    
    # Extract parameters
    base_model = data['base_model']
    domain = data['domain']
    dataset = data['dataset']
    api_key = data['api_key']
    parameters = data.get('parameters', {})
    
    try:
        result = start_fine_tuning(
            base_model=base_model,
            domain=domain,
            dataset=dataset,
            api_key=api_key,
            parameters=parameters
        )
        
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@fine_tuning_bp.route('/status/<job_id>', methods=['GET'])
def get_status(job_id):
    """
    Get status of a fine-tuning job
    
    Args:
        job_id: Fine-tuning job ID
        
    Returns:
        JSON object with job status
    """
    try:
        status = get_fine_tuning_status(job_id)
        
        if not status:
            return jsonify({"error": f"Job '{job_id}' not found"}), 404
            
        return jsonify(status)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@fine_tuning_bp.route('/models', methods=['GET'])
def list_models():
    """
    List fine-tuned models
    
    Returns:
        JSON list of fine-tuned models
    """
    try:
        models = list_fine_tuned_models()
        return jsonify(models)
    except Exception as e:
        return jsonify({"error": str(e)}), 500
